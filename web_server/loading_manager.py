"""
加载状态管理器
管理应用中的加载状态和用户反馈
"""

from dash import html, callback, Input, Output, State, ALL, MATCH
import dash_bootstrap_components as dbc
from typing import Dict, List, Optional, Any
import time
import threading
from collections import defaultdict


class LoadingManager:
    """加载状态管理器"""
    
    def __init__(self):
        self.loading_states = defaultdict(bool)
        self.loading_messages = defaultdict(str)
        self.loading_start_times = defaultdict(float)
        self.lock = threading.Lock()
    
    def start_loading(self, component_id: str, message: str = "加载中..."):
        """开始加载状态"""
        with self.lock:
            self.loading_states[component_id] = True
            self.loading_messages[component_id] = message
            self.loading_start_times[component_id] = time.time()
    
    def stop_loading(self, component_id: str):
        """停止加载状态"""
        with self.lock:
            self.loading_states[component_id] = False
            if component_id in self.loading_start_times:
                duration = time.time() - self.loading_start_times[component_id]
                print(f"组件 {component_id} 加载完成，耗时: {duration:.2f}秒")
                del self.loading_start_times[component_id]
    
    def is_loading(self, component_id: str) -> bool:
        """检查是否正在加载"""
        with self.lock:
            return self.loading_states.get(component_id, False)
    
    def get_loading_message(self, component_id: str) -> str:
        """获取加载消息"""
        with self.lock:
            return self.loading_messages.get(component_id, "加载中...")
    
    def get_all_loading_states(self) -> Dict[str, Dict]:
        """获取所有加载状态"""
        with self.lock:
            return {
                component_id: {
                    'loading': is_loading,
                    'message': self.loading_messages.get(component_id, "加载中..."),
                    'duration': time.time() - self.loading_start_times.get(component_id, time.time())
                }
                for component_id, is_loading in self.loading_states.items()
            }


def create_loading_overlay(component_id: str, loading_text: str = "加载中...", 
                          loading_type: str = "default") -> html.Div:
    """创建加载遮罩层"""
    
    loading_configs = {
        "default": {"spinner": "fas fa-spinner fa-spin", "color": "#0d6efd"},
        "dots": {"spinner": "fas fa-circle-notch fa-spin", "color": "#198754"},
        "pulse": {"spinner": "fas fa-heart fa-beat", "color": "#dc3545"},
        "bounce": {"spinner": "fas fa-basketball-ball fa-bounce", "color": "#fd7e14"}
    }
    
    config = loading_configs.get(loading_type, loading_configs["default"])
    
    return html.Div([
        html.Div([
            html.Div([
                html.I(className=config["spinner"], 
                      style={"fontSize": "2rem", "color": config["color"]}),
                html.P(loading_text, className="mt-2 mb-0 text-muted")
            ], className="text-center")
        ], className="d-flex justify-content-center align-items-center h-100")
    ], 
    id=f"loading-overlay-{component_id}",
    className="position-absolute w-100 h-100 bg-white bg-opacity-75 d-flex justify-content-center align-items-center",
    style={"top": 0, "left": 0, "zIndex": 1000, "display": "none"})


def create_progress_bar(component_id: str, progress: int = 0, 
                       show_percentage: bool = True) -> dbc.Progress:
    """创建进度条"""
    return dbc.Progress(
        id=f"progress-{component_id}",
        value=progress,
        label=f"{progress}%" if show_percentage else "",
        color="primary",
        striped=True,
        animated=True,
        className="mb-3"
    )


def create_step_indicator(steps: List[str], current_step: int = 0) -> html.Div:
    """创建步骤指示器"""
    step_items = []
    
    for i, step in enumerate(steps):
        if i < current_step:
            # 已完成的步骤
            step_class = "bg-success text-white"
            icon = "fas fa-check"
        elif i == current_step:
            # 当前步骤
            step_class = "bg-primary text-white"
            icon = "fas fa-spinner fa-spin"
        else:
            # 未开始的步骤
            step_class = "bg-light text-muted"
            icon = "fas fa-circle"
        
        step_items.append(
            html.Div([
                html.Div([
                    html.I(className=icon)
                ], className=f"rounded-circle d-flex justify-content-center align-items-center {step_class}",
                   style={"width": "30px", "height": "30px"}),
                html.Small(step, className="mt-1 text-center")
            ], className="d-flex flex-column align-items-center")
        )
        
        # 添加连接线（除了最后一个步骤）
        if i < len(steps) - 1:
            line_class = "bg-success" if i < current_step else "bg-light"
            step_items.append(
                html.Div(className=f"flex-grow-1 {line_class}", 
                        style={"height": "2px", "marginTop": "15px"})
            )
    
    return html.Div(
        step_items,
        className="d-flex align-items-start justify-content-between w-100 mb-4"
    )


def create_loading_skeleton(rows: int = 3, cols: int = 1) -> html.Div:
    """创建骨架屏加载效果"""
    skeleton_rows = []
    
    for i in range(rows):
        skeleton_cols = []
        for j in range(cols):
            skeleton_cols.append(
                html.Div(
                    className="bg-light rounded",
                    style={
                        "height": "20px",
                        "animation": "pulse 1.5s ease-in-out infinite"
                    }
                )
            )
        
        if cols > 1:
            skeleton_rows.append(
                dbc.Row([
                    dbc.Col(col, width=12//cols) for col in skeleton_cols
                ], className="mb-2")
            )
        else:
            skeleton_rows.extend(skeleton_cols)
            if i < rows - 1:
                skeleton_rows.append(html.Div(style={"height": "10px"}))
    
    return html.Div(skeleton_rows, className="p-3")


def create_loading_card(title: str, loading_text: str = "数据加载中...",
                       card_height: str = "200px") -> dbc.Card:
    """创建加载卡片"""
    return dbc.Card([
        dbc.CardHeader([
            html.H6(title, className="mb-0")
        ]),
        dbc.CardBody([
            html.Div([
                html.I(className="fas fa-spinner fa-spin fa-2x text-primary mb-3"),
                html.P(loading_text, className="text-muted mb-0")
            ], className="text-center d-flex flex-column justify-content-center h-100")
        ])
    ], style={"height": card_height})


def create_timeout_warning(timeout_seconds: int = 30) -> dbc.Alert:
    """创建超时警告"""
    return dbc.Alert([
        html.I(className="fas fa-clock me-2"),
        f"加载时间较长，请耐心等待。如果超过 {timeout_seconds} 秒仍未完成，请刷新页面重试。"
    ], color="warning", dismissable=True, className="mt-3")


def create_retry_button(component_id: str, retry_text: str = "重新加载") -> dbc.Button:
    """创建重试按钮"""
    return dbc.Button([
        html.I(className="fas fa-redo me-1"),
        retry_text
    ], 
    id=f"retry-{component_id}",
    color="outline-primary",
    size="sm",
    className="mt-2")


class LoadingStates:
    """加载状态常量"""
    IDLE = "idle"
    LOADING = "loading"
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"


def create_smart_loading_component(component_id: str, 
                                 content_component: Any,
                                 loading_text: str = "加载中...",
                                 error_component: Optional[Any] = None,
                                 timeout_seconds: int = 30) -> html.Div:
    """创建智能加载组件，包含加载、成功、错误、超时状态"""
    
    return html.Div([
        # 加载状态
        html.Div([
            create_loading_overlay(component_id, loading_text),
            create_timeout_warning(timeout_seconds)
        ], id=f"loading-state-{component_id}", style={"display": "none"}),
        
        # 内容状态
        html.Div(
            content_component,
            id=f"content-state-{component_id}"
        ),
        
        # 错误状态
        html.Div(
            error_component or create_retry_button(component_id),
            id=f"error-state-{component_id}",
            style={"display": "none"}
        )
    ], id=f"smart-loading-{component_id}", className="position-relative")


# 全局加载管理器实例
loading_manager = LoadingManager()


# CSS动画样式
LOADING_CSS = """
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.loading-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.loading-slide-down {
    animation: slideDown 0.3s ease-in-out;
}

.skeleton-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}
"""


def get_loading_css() -> str:
    """获取加载相关的CSS样式"""
    return LOADING_CSS
