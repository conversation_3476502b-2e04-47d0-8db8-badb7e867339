"""
重构后的策略回测分析系统主应用
采用模块化设计，提升性能和用户体验
"""

import dash
from dash import html
import dash_bootstrap_components as dbc

# 导入自定义模块
from config import APP_CONFIG, UI_CONFIG
from layout import create_layout, get_external_stylesheets, get_custom_css
import callbacks  # 导入回调函数模块


def create_app():
    """创建Dash应用实例"""
    app = dash.Dash(
        __name__,
        external_stylesheets=get_external_stylesheets(),
        suppress_callback_exceptions=APP_CONFIG['suppress_callback_exceptions'],
        title=UI_CONFIG['page_title'],
        update_title='Loading...',
        meta_tags=[
            {"name": "viewport", "content": "width=device-width, initial-scale=1"},
            {"name": "description", "content": "策略回测分析系统 - 专业的量化投资回测平台"},
            {"name": "keywords", "content": "策略回测,量化投资,投资分析,风险管理"}
        ]
    )
    
    # 设置应用布局
    app.layout = html.Div([
        # 主要布局
        create_layout()
    ])

    # 添加自定义CSS
    app.index_string = '''
    <!DOCTYPE html>
    <html>
        <head>
            {%metas%}
            <title>{%title%}</title>
            {%favicon%}
            {%css%}
            ''' + get_custom_css() + '''
        </head>
        <body>
            {%app_entry%}
            <footer>
                {%config%}
                {%scripts%}
                {%renderer%}
            </footer>
        </body>
    </html>
    '''
    
    return app


def main():
    """主函数"""
    app = create_app()
    
    # 启动应用
    app.run_server(
        host=APP_CONFIG['host'],
        port=APP_CONFIG['port'],
        debug=APP_CONFIG['debug']
    )


if __name__ == '__main__':
    main()
