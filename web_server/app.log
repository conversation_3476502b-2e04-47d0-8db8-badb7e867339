2025-07-31 17:31:30,907 - error_handler - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "/mnt/Data2/auto_task/web_server/app_new.py", line 13, in <module>
    import callbacks  # 导入回调函数模块
    ^^^^^^^^^^^^^^^^
  File "/mnt/Data2/auto_task/web_server/callbacks.py", line 161
    try:
    ^^^
SyntaxError: expected 'except' or 'finally' block
2025-07-31 17:33:39,171 - numexpr.utils - INFO - Note: detected 256 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-07-31 17:33:39,171 - numexpr.utils - INFO - Note: NumExpr detected 256 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 17:33:39,171 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 17:33:39,370 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-07-31 17:34:30,817 - numexpr.utils - INFO - Note: detected 256 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-07-31 17:34:30,817 - numexpr.utils - INFO - Note: NumExpr detected 256 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 17:34:30,817 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 17:34:31,061 - dash.dash - INFO - Dash is running on http://0.0.0.0:8051/

2025-07-31 17:34:31,064 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8051
 * Running on http://*************:8051
2025-07-31 17:34:31,064 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 17:34:34,855 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:34] "GET /?user=admin&path=/mnt/Data2/Jupyter/2025/行业SUE/bt_data/中性100_70%现货_SUE_L2增强 HTTP/1.1" 200 -
2025-07-31 17:34:35,073 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "GET /_dash-layout HTTP/1.1" 200 -
2025-07-31 17:34:35,113 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "GET /_dash-dependencies HTTP/1.1" 200 -
2025-07-31 17:34:35,745 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:35,748 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:35,752 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "[36mGET /_dash-component-suites/dash/dcc/async-dropdown.js HTTP/1.1[0m" 304 -
2025-07-31 17:34:35,789 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "[36mGET /_dash-component-suites/dash/dash_table/async-highlight.js HTTP/1.1[0m" 304 -
2025-07-31 17:34:35,790 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:35] "[36mGET /_dash-component-suites/dash/dash_table/async-table.js HTTP/1.1[0m" 304 -
2025-07-31 17:34:36,023 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:34:36,024 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,323 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,364 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-07-31 17:34:36,364 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,366 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,407 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,603 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,916 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,919 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,923 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,924 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:34:36,925 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:36,957 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:34:36,958 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:36] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:37,208 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:37] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:34:38,043 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:34:38] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,053 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:35:06,054 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,056 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,452 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,455 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,458 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:06,462 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-07-31 17:35:06,462 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:06] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,780 - error_handler - ERROR - 应用错误在 update_account_options: 策略名称不能为空
2025-07-31 17:35:08,781 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,812 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,817 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,821 - error_handler - ERROR - 应用错误在 update_charts: 策略名称不能为空
2025-07-31 17:35:08,822 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:08,824 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:08] "POST /_dash-update-component HTTP/1.1" 200 -
2025-07-31 17:35:09,318 - werkzeug - INFO - 127.0.0.1 - - [31/Jul/2025 17:35:09] "POST /_dash-update-component HTTP/1.1" 200 -
