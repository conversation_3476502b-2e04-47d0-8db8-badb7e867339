"""
回调函数模块
定义所有的Dash回调函数
"""

from dash import callback, Input, Output, dcc
from urllib.parse import parse_qs
import pandas as pd
from typing import List, Dict, Any, Tuple

from data_handler import data_handler
from chart_generator import chart_generator
from config import PATH_MAPPINGS, METRICS_CONFIG, UI_CONFIG
from performance_monitor import monitor_performance
from error_handler import (
    handle_errors, create_error_display, create_loading_component,
    create_empty_state, validate_data_input, log_user_action,
    DataLoadError, ValidationError
)


@callback(
    [Output('store', 'data'),
     Output('data-pth', 'value'),
     Output('strage-dropdown', 'options'),
     Output('strage-dropdown', 'value')],
    [Input('url', 'search')]
)
def update_components_from_url(search: str) -> Tuple[Dict, str, List[Dict], str]:
    """解析URL参数并更新组件"""
    if not search:
        return {}, '', [], ''
    
    try:
        params = parse_qs(search[1:])  # 去掉'?'字符
        user = params.get('user', [''])[0] if params.get('user') else ''
        path = params.get('path', [''])[0] if params.get('path') else ''
        
        # 处理预定义路径映射
        if path in PATH_MAPPINGS:
            mapping = PATH_MAPPINGS[path]
            user = mapping['user']
            path = mapping['path']
        
        if not path:
            return {}, '', [], ''
        
        # 获取策略选项
        strategy_options = data_handler.get_strategy_options(user, path)
        strategy_name = strategy_options[0]['value'] if strategy_options else ''
        
        return {"user": user}, path, strategy_options, strategy_name
        
    except Exception as e:
        print(f"Error parsing URL: {e}")
        return {}, '', [], ''


@callback(
    Output('account-id-dropdown', 'options'),
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value')]
)
@handle_errors(return_on_error=[])
@monitor_performance('update_account_options')
def update_account_options(store: Dict, data_path: str, strategy: str) -> List[Dict]:
    """更新账户选项"""
    try:
        validate_data_input(store, "用户信息", required=True, data_type=dict)
        validate_data_input(data_path, "数据路径", required=True)
        validate_data_input(strategy, "策略名称", required=True)

        user = store.get('user', '')
        if not user:
            raise ValidationError("用户信息不能为空", "user")

        log_user_action("更新账户选项", {"user": user, "data_path": data_path, "strategy": strategy})

        options = data_handler.get_account_options(user, data_path, strategy)
        if not options:
            raise DataLoadError("未找到可用的账户数据", f"{data_path}/{strategy}")

        return options

    except Exception as e:
        if isinstance(e, (DataLoadError, ValidationError)):
            raise
        raise DataLoadError(f"获取账户选项失败: {str(e)}", data_path)


@callback(
    Output('period-dropdown', 'options'),
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     Input('account-id-dropdown', 'value')]
)
def update_period_options(store: Dict, data_path: str, strategy: str, account_id: str) -> List[str]:
    """更新周期选项"""
    if not all([store, data_path, strategy, account_id]):
        return []
    
    user = store.get('user', '')
    if not user:
        return []
    
    periods = data_handler.get_period_options(user, data_path, strategy, account_id)
    return [{'label': period, 'value': period} for period in periods]


@callback(
    Output('metrics-checklist', 'options'),
    [Input('account-id-dropdown', 'value')]
)
def update_metrics_options(account_id: str) -> List[Dict]:
    """更新指标选项"""
    if not account_id:
        return []
    
    # 根据账户类型返回相应的指标
    for account_type, metrics in METRICS_CONFIG.items():
        if account_type in account_id:
            return metrics
    
    # 默认返回全部账户汇总的指标
    return METRICS_CONFIG.get('全部账户汇总', [])


@callback(
    [Output('dynamic-graphs', 'children'),
     Output('tempdata', 'data')],
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     Input('account-id-dropdown', 'value'),
     Input('metrics-checklist', 'value'),
     Input('period-dropdown', 'value')]
)
@handle_errors(return_on_error=([], []))
@monitor_performance('update_charts')
def update_charts(store: Dict, data_path: str, strategy: str, account_id: str,
                 selected_metrics: List[str], period: str) -> Tuple[List, List[Dict]]:
    """更新图表"""
    try:
        # 验证输入参数
        validate_data_input(store, "用户信息", required=True, data_type=dict)
        validate_data_input(data_path, "数据路径", required=True)
        validate_data_input(strategy, "策略名称", required=True)
        validate_data_input(account_id, "账户ID", required=True)

        user = store.get('user', '')
        if not user:
            raise ValidationError("用户信息不能为空", "user")

        log_user_action("更新图表", {
            "user": user, "data_path": data_path, "strategy": strategy,
            "account_id": account_id, "period": period
        })

        # 加载数据
        strategy_path = data_handler.get_real_path(user, data_path, strategy)
        daily_return_df = data_handler.load_daily_return_data(user, data_path, strategy, account_id)

        if daily_return_df is None:
            raise DataLoadError("无法加载日收益数据", f"{data_path}/{strategy}/{account_id}")

        # 增强数据（添加额外指标）
        if selected_metrics:
            daily_return_df = data_handler.enhance_data_with_metrics(
                daily_return_df, strategy_path, strategy, account_id, selected_metrics
            )
        
        # 应用周期过滤
        if period and period != '全部':
            # 对净值相关列进行累积收益计算
            value_columns = [col for col in daily_return_df.columns 
                           if any(keyword in col for keyword in ['净值', '指数', '收益'])]
            
            for col in value_columns:
                if col in daily_return_df.columns:
                    daily_return_df[col] = daily_return_df[col].pct_change().fillna(0)
                    daily_return_df[col] = (daily_return_df[col] + 1).cumprod()
            
            daily_return_df = data_handler.period_filter(daily_return_df, period)
        
        # 加载业绩数据用于绘制回撤区域
        performance_df = data_handler.load_performance_data(user, data_path, strategy, account_id, period)
        
        # 生成图表
        if selected_metrics:
            fig = chart_generator.create_metrics_chart(daily_return_df, selected_metrics, account_id)
        else:
            # 检查是否有净值数据
            if any(col in daily_return_df.columns for col in ['策略净值', '基准指数']):
                fig = chart_generator.create_main_chart(daily_return_df, account_id, performance_df)
            else:
                # 只有balance数据
                fig = chart_generator.create_simple_balance_chart(daily_return_df, account_id)
        
        # 计算图表高度
        if selected_metrics:
            total_height = chart_generator.heights['main_chart'] + len(selected_metrics) * chart_generator.heights['sub_chart']
        else:
            total_height = chart_generator.heights['main_chart']
        
        graph_component = dcc.Graph(
            figure=fig,
            style={'height': f'{total_height}px'},
            config=UI_CONFIG['chart_config']
        )
        
        return [graph_component], daily_return_df.to_dict('records')

    except Exception as e:
        if isinstance(e, (DataLoadError, ValidationError)):
            raise
        raise DataLoadError(f"图表更新失败: {str(e)}", data_path)


@callback(
    Output('table', 'data'),
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     Input('account-id-dropdown', 'value'),
     Input('period-dropdown', 'value')]
)
def update_performance_table(store: Dict, data_path: str, strategy: str, 
                           account_id: str, period: str) -> List[Dict]:
    """更新业绩表格"""
    if not all([store, data_path, strategy, account_id]):
        return []
    
    user = store.get('user', '')
    if not user:
        return []
    
    try:
        performance_df = data_handler.load_performance_data(user, data_path, strategy, account_id, period)
        if performance_df is not None:
            return performance_df.to_dict('records')
        return []
        
    except Exception as e:
        print(f"Error updating performance table: {e}")
        return []


@callback(
    Output('run-info', 'data'),
    [Input('store', 'data'),
     Input('data-pth', 'value'),
     Input('strage-dropdown', 'value'),
     Input('tempdata', 'data'),
     Input('period-dropdown', 'value')]
)
def update_run_info(store: Dict, data_path: str, strategy: str, 
                   temp_data: List[Dict], period: str) -> List[Dict]:
    """更新运行信息"""
    if not all([store, data_path, strategy]):
        return []
    
    user = store.get('user', '')
    if not user:
        return []
    
    try:
        config_data = data_handler.load_config_data(user, data_path, strategy)
        if not config_data:
            return []
        
        # 构建显示数据
        rows = {}
        for key in ['start_date', 'end_date', 'base_index', 'freq', 'price_mode', 'super_trader']:
            if key in config_data:
                rows[key] = {'values': config_data[key]}
        
        # 如果有周期过滤，更新开始和结束日期
        if period and period != '全部' and temp_data:
            temp_df = pd.DataFrame(temp_data)
            if 'date' in temp_df.columns:
                dates = temp_df['date'].sort_values().tolist()
                if dates:
                    rows['start_date'] = {'values': dates[0]}
                    rows['end_date'] = {'values': dates[-1]}
        
        df = pd.DataFrame(rows)
        return df.to_dict('records')
        
    except Exception as e:
        print(f"Error updating run info: {e}")
        return []


@callback(
    Output('dynamic-title', 'children'),
    [Input('data-pth', 'value')]
)
def update_title(data_path: str) -> str:
    """更新动态标题"""
    if not data_path:
        return ""
    
    try:
        title = data_path.split('/')[-1]
        return title
    except Exception:
        return ""
