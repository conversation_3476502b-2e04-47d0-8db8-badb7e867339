# Financial Strategy Backtesting Dashboard v2.0

一个现代化的金融策略回测可视化仪表板，基于 Dash 框架构建，提供交互式图表和性能分析功能。

## 🚀 新版本特性

### ✨ 主要改进

- **🏗️ 模块化架构**: 完全重构代码，采用模块化设计，提高可维护性
- **🎨 现代化UI**: 使用 Bootstrap 5 主题，响应式设计，支持移动端
- **⚡ 性能优化**: 实现多级缓存系统，显著提升数据加载速度
- **🛡️ 错误处理**: 完善的错误处理机制，用户友好的错误提示
- **📊 性能监控**: 内置性能监控系统，实时追踪应用性能
- **🔄 加载状态**: 智能加载指示器，提升用户体验
- **🧪 测试覆盖**: 完整的测试套件，确保代码质量

### 🔧 技术栈

- **前端**: Dash, Plotly, Bootstrap 5, HTML5, CSS3
- **后端**: Python, Pandas, NumPy
- **缓存**: 内存缓存 + Redis 支持
- **部署**: Gunicorn, Docker 支持
- **测试**: unittest, Mock

## 📁 项目结构

```
web_server/
├── app_new.py              # 主应用文件
├── config.py               # 配置模块
├── data_handler.py         # 数据处理模块
├── chart_generator.py      # 图表生成模块
├── layout.py               # 布局模块
├── callbacks.py            # 回调函数模块
├── cache_manager.py        # 缓存管理模块
├── error_handler.py        # 错误处理模块
├── performance_monitor.py  # 性能监控模块
├── loading_manager.py      # 加载状态管理模块
├── deploy_config.py        # 部署配置模块
├── start_app.py           # 启动脚本
├── test_simple.py         # 测试套件
├── requirements.txt       # 依赖列表
└── README.md             # 项目文档
```

## 🛠️ 安装和运行

### 环境要求

- Python 3.8+
- 推荐使用虚拟环境

### 快速开始

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行健康检查**
   ```bash
   python start_app.py health
   ```

3. **运行测试**
   ```bash
   python start_app.py test
   ```

4. **启动开发服务器**
   ```bash
   python start_app.py dev
   ```

5. **访问应用**
   
   打开浏览器访问: http://localhost:8051

### 启动选项

```bash
# 开发模式（默认）
python start_app.py dev

# 指定主机和端口
python start_app.py dev --host 0.0.0.0 --port 8080

# 启用/禁用调试模式
python start_app.py dev --debug
python start_app.py dev --no-debug

# 生产模式（需要安装 gunicorn）
python start_app.py prod

# 运行测试
python start_app.py test

# 健康检查
python start_app.py health
```

## 📊 功能特性

### 数据可视化
- **策略净值曲线**: 实时展示策略表现
- **基准对比**: 与基准指数对比分析
- **回撤分析**: 可视化最大回撤区域
- **性能指标**: 多维度性能指标展示
- **交互式图表**: 支持缩放、平移、悬停提示

### 数据管理
- **多用户支持**: 支持管理员和普通用户
- **数据缓存**: 智能缓存机制，提升加载速度
- **文件格式**: 支持 Feather 格式，快速 I/O
- **数据过滤**: 支持时间周期过滤

### 用户体验
- **响应式设计**: 适配各种屏幕尺寸
- **加载状态**: 智能加载指示器
- **错误处理**: 用户友好的错误提示
- **性能监控**: 实时性能指标

## 🔧 配置说明

### 环境变量

```bash
# 应用环境 (development/production/testing)
FLASK_ENV=development

# 生产环境必需
SECRET_KEY=your-secret-key

# Redis 配置（可选）
REDIS_URL=redis://localhost:6379/0

# 端口配置
PORT=8051
```

### 配置文件

主要配置在 `config.py` 中：

- **UI_CONFIG**: 界面配置
- **THEME_CONFIG**: 主题配置  
- **CACHE_CONFIG**: 缓存配置
- **PERFORMANCE_CONFIG**: 性能配置

## 🧪 测试

### 运行测试

```bash
# 运行完整测试套件
python test_simple.py

# 使用启动脚本运行测试
python start_app.py test
```

### 测试覆盖

- ✅ 数据处理模块测试
- ✅ 缓存系统测试
- ✅ 错误处理测试
- ✅ 性能监控测试
- ✅ 模块导入测试
- ✅ 集成测试

## 📈 性能优化

### 缓存策略
- **内存缓存**: 快速访问常用数据
- **Redis 缓存**: 分布式缓存支持
- **装饰器缓存**: 函数级别缓存

### 数据优化
- **Feather 格式**: 快速数据序列化
- **懒加载**: 按需加载数据
- **数据压缩**: 减少内存占用

### 前端优化
- **组件缓存**: 避免重复渲染
- **异步加载**: 非阻塞数据加载
- **响应式设计**: 优化移动端体验

## 🚀 部署

### 开发环境
```bash
python start_app.py dev
```

### 生产环境
```bash
# 安装 Gunicorn
pip install gunicorn

# 启动生产服务器
python start_app.py prod
```

### Docker 部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8051

CMD ["python", "start_app.py", "prod"]
```

## 🔍 监控和日志

### 性能监控
- 函数执行时间追踪
- 系统资源监控
- 缓存命中率统计
- 慢查询检测

### 日志系统
- 结构化日志记录
- 日志轮转支持
- 不同级别日志
- 错误追踪

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📝 更新日志

### v2.0.0 (2025-08-01)
- 🏗️ 完全重构，采用模块化架构
- 🎨 现代化 UI 设计
- ⚡ 性能优化和缓存系统
- 🛡️ 完善错误处理
- 📊 性能监控系统
- 🧪 完整测试覆盖

### v1.0.0
- 基础功能实现
- 简单的数据可视化
- 基本的用户界面

## 📄 许可证

MIT License

## 📞 支持

如有问题或建议，请创建 Issue 或联系开发团队。
