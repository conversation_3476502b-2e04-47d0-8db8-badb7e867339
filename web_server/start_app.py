#!/usr/bin/env python3
"""
应用启动脚本
支持不同环境的启动配置
"""

import sys
import os
import argparse
import signal
import time
from typing import Optional

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from deploy_config import create_app_with_config, get_server_config, health_check


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在关闭应用...")
    sys.exit(0)


def start_development_server(environment: str = 'development', 
                           host: Optional[str] = None, 
                           port: Optional[int] = None,
                           debug: Optional[bool] = None):
    """启动开发服务器"""
    try:
        # 创建应用和配置
        app, config = create_app_with_config(environment)
        
        # 获取服务器配置
        server_config = get_server_config(config)
        
        # 覆盖配置（如果提供了参数）
        if host:
            server_config['host'] = host
        if port:
            server_config['port'] = port
        if debug is not None:
            server_config['debug'] = debug
        
        print(f"🚀 启动 {config.get('app_name')} v{config.get('version')}")
        print(f"📍 环境: {environment}")
        print(f"🌐 地址: http://{server_config['host']}:{server_config['port']}")
        print(f"🔧 调试模式: {'开启' if server_config['debug'] else '关闭'}")
        print("-" * 50)
        
        # 运行健康检查
        health_result = health_check()
        if health_result['status'] != 'healthy':
            print("⚠️  健康检查失败，但仍将启动应用")
            for check, status in health_result['checks'].items():
                if not status:
                    print(f"   ❌ {check}")
        
        # 启动应用
        app.run(**server_config)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


def start_production_server(environment: str = 'production',
                          host: Optional[str] = None,
                          port: Optional[int] = None):
    """启动生产服务器（使用Gunicorn）"""
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        # 创建应用和配置
        app, config = create_app_with_config(environment)
        
        # 获取服务器配置
        server_config = get_server_config(config)
        
        # 覆盖配置
        if host:
            server_config['host'] = host
        if port:
            server_config['port'] = port
        
        print(f"🚀 启动生产服务器 {config.get('app_name')} v{config.get('version')}")
        print(f"🌐 地址: http://{server_config['host']}:{server_config['port']}")
        print(f"👥 工作进程: {config.get('workers', 1)}")
        print("-" * 50)
        
        # 运行健康检查
        health_result = health_check()
        if health_result['status'] != 'healthy':
            print("❌ 健康检查失败，无法启动生产服务器")
            sys.exit(1)
        
        # Gunicorn配置
        gunicorn_config = [
            'gunicorn',
            '--bind', f"{server_config['host']}:{server_config['port']}",
            '--workers', str(config.get('workers', 1)),
            '--timeout', str(config.get('timeout', 300)),
            '--worker-class', 'sync',
            '--max-requests', '1000',
            '--max-requests-jitter', '100',
            '--preload',
            'app_new:app'
        ]
        
        # 启动Gunicorn
        sys.argv = gunicorn_config
        wsgi.run()
        
    except ImportError:
        print("❌ 未安装Gunicorn，无法启动生产服务器")
        print("请运行: pip install gunicorn")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 生产服务器启动失败: {e}")
        sys.exit(1)


def run_health_check():
    """运行健康检查"""
    print("🔍 运行健康检查...")
    result = health_check()
    
    print(f"状态: {result['status']}")
    print(f"时间: {result['timestamp']}")
    print("检查项目:")
    
    for check, status in result['checks'].items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {check}")
    
    if result['status'] == 'healthy':
        print("\n✅ 应用健康状态良好")
        return True
    else:
        print("\n❌ 应用健康检查失败")
        return False


def run_tests():
    """运行测试"""
    print("🧪 运行应用测试...")
    
    try:
        # 运行简化测试
        import subprocess
        result = subprocess.run([
            sys.executable, 'test_simple.py'
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            print("✅ 所有测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ 测试失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Financial Strategy Dashboard 启动脚本')
    
    parser.add_argument('command', choices=['dev', 'prod', 'test', 'health'], 
                       help='启动命令')
    parser.add_argument('--env', default=None, 
                       help='环境 (development/production/testing)')
    parser.add_argument('--host', default=None, 
                       help='主机地址')
    parser.add_argument('--port', type=int, default=None, 
                       help='端口号')
    parser.add_argument('--debug', action='store_true', 
                       help='启用调试模式')
    parser.add_argument('--no-debug', action='store_true', 
                       help='禁用调试模式')
    
    args = parser.parse_args()
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 确定环境
    if args.env:
        environment = args.env
    elif args.command == 'prod':
        environment = 'production'
    elif args.command == 'test':
        environment = 'testing'
    else:
        environment = 'development'
    
    # 确定调试模式
    debug = None
    if args.debug:
        debug = True
    elif args.no_debug:
        debug = False
    
    # 执行命令
    if args.command == 'dev':
        start_development_server(environment, args.host, args.port, debug)
    elif args.command == 'prod':
        start_production_server(environment, args.host, args.port)
    elif args.command == 'health':
        success = run_health_check()
        sys.exit(0 if success else 1)
    elif args.command == 'test':
        success = run_tests()
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
