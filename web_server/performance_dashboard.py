"""
性能仪表板
提供系统性能监控和分析界面
"""

from dash import dcc, html, callback, Input, Output
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import pandas as pd

from performance_monitor import get_performance_dashboard, clear_performance_stats
from cache_manager import get_cache_stats, clear_cache


def create_performance_layout():
    """创建性能仪表板布局"""
    return dbc.Container([
        # 页面标题
        dbc.Row([
            dbc.Col([
                html.H2([
                    html.I(className="fas fa-tachometer-alt me-2"),
                    "性能监控仪表板"
                ], className="text-primary mb-4")
            ])
        ]),
        
        # 刷新和清理按钮
        dbc.Row([
            dbc.Col([
                dbc.ButtonGroup([
                    dbc.<PERSON><PERSON>([
                        html.I(className="fas fa-sync-alt me-1"),
                        "刷新数据"
                    ], id="refresh-performance", color="primary", size="sm"),
                    dbc.<PERSON><PERSON>([
                        html.I(className="fas fa-trash me-1"),
                        "清理缓存"
                    ], id="clear-cache", color="warning", size="sm"),
                    dbc.Button([
                        html.I(className="fas fa-eraser me-1"),
                        "重置统计"
                    ], id="clear-stats", color="danger", size="sm")
                ])
            ], width="auto"),
            dbc.Col([
                html.Div(id="performance-alerts")
            ])
        ], className="mb-4"),
        
        # 系统指标卡片
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-server me-2"),
                            "系统资源"
                        ], className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="system-metrics")
                    ])
                ])
            ], width=4),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-memory me-2"),
                            "缓存状态"
                        ], className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="cache-metrics")
                    ])
                ])
            ], width=4),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-clock me-2"),
                            "应用状态"
                        ], className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="app-metrics")
                    ])
                ])
            ], width=4)
        ], className="mb-4"),
        
        # 性能图表
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-chart-line me-2"),
                            "函数性能分析"
                        ], className="mb-0")
                    ]),
                    dbc.CardBody([
                        dcc.Graph(id="function-performance-chart")
                    ])
                ])
            ], width=8),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-lightbulb me-2"),
                            "优化建议"
                        ], className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="performance-recommendations")
                    ])
                ])
            ], width=4)
        ], className="mb-4"),
        
        # 详细统计表格
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5([
                            html.I(className="fas fa-table me-2"),
                            "详细性能统计"
                        ], className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="detailed-stats")
                    ])
                ])
            ])
        ]),
        
        # 自动刷新
        dcc.Interval(
            id='performance-interval',
            interval=10*1000,  # 10秒刷新一次
            n_intervals=0
        )
    ], fluid=True)


@callback(
    [Output('system-metrics', 'children'),
     Output('cache-metrics', 'children'),
     Output('app-metrics', 'children'),
     Output('function-performance-chart', 'figure'),
     Output('performance-recommendations', 'children'),
     Output('detailed-stats', 'children'),
     Output('performance-alerts', 'children')],
    [Input('performance-interval', 'n_intervals'),
     Input('refresh-performance', 'n_clicks')]
)
def update_performance_dashboard(n_intervals, refresh_clicks):
    """更新性能仪表板"""
    try:
        dashboard_data = get_performance_dashboard()
        summary = dashboard_data['summary']
        recommendations = dashboard_data['recommendations']
        function_stats = dashboard_data['function_stats']
        
        # 系统指标
        system_metrics = summary.get('system_metrics', {})
        system_cards = create_metric_cards([
            ("CPU使用率", f"{system_metrics.get('cpu_percent', 0):.1f}%", "fas fa-microchip"),
            ("内存使用率", f"{system_metrics.get('memory_percent', 0):.1f}%", "fas fa-memory"),
            ("磁盘使用率", f"{system_metrics.get('disk_percent', 0):.1f}%", "fas fa-hdd")
        ])
        
        # 缓存指标
        cache_metrics = summary.get('cache_metrics', {})
        if isinstance(cache_metrics, dict) and 'total_entries' in cache_metrics:
            cache_cards = create_metric_cards([
                ("缓存条目", str(cache_metrics.get('total_entries', 0)), "fas fa-database"),
                ("活跃条目", str(cache_metrics.get('active_entries', 0)), "fas fa-check-circle"),
                ("内存使用", f"{cache_metrics.get('memory_usage_mb', 0):.1f}MB", "fas fa-memory")
            ])
        else:
            cache_cards = create_metric_cards([
                ("缓存类型", "Redis", "fas fa-database"),
                ("状态", "运行中", "fas fa-check-circle"),
                ("连接", "正常", "fas fa-link")
            ])
        
        # 应用指标
        uptime_hours = summary.get('uptime_seconds', 0) / 3600
        total_calls = summary.get('total_function_calls', 0)
        app_cards = create_metric_cards([
            ("运行时间", f"{uptime_hours:.1f}小时", "fas fa-clock"),
            ("函数调用", str(total_calls), "fas fa-code"),
            ("状态", "正常", "fas fa-heart")
        ])
        
        # 函数性能图表
        performance_chart = create_function_performance_chart(function_stats)
        
        # 优化建议
        recommendations_list = create_recommendations_list(recommendations)
        
        # 详细统计表格
        detailed_table = create_detailed_stats_table(function_stats)
        
        # 性能警告
        alerts = create_performance_alerts(system_metrics, cache_metrics)
        
        return (system_cards, cache_cards, app_cards, performance_chart, 
                recommendations_list, detailed_table, alerts)
        
    except Exception as e:
        error_msg = html.Div([
            dbc.Alert(f"获取性能数据失败: {e}", color="danger")
        ])
        empty_fig = go.Figure()
        return error_msg, error_msg, error_msg, empty_fig, error_msg, error_msg, error_msg


def create_metric_cards(metrics):
    """创建指标卡片"""
    cards = []
    for label, value, icon in metrics:
        card = html.Div([
            html.I(className=f"{icon} text-primary me-2"),
            html.Span(label, className="text-muted small d-block"),
            html.Strong(value, className="h5 mb-0")
        ], className="text-center p-2")
        cards.append(card)
    
    return html.Div(cards)


def create_function_performance_chart(function_stats):
    """创建函数性能图表"""
    if not function_stats:
        return go.Figure().add_annotation(
            text="暂无函数性能数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False
        )
    
    # 准备数据
    functions = []
    avg_times = []
    call_counts = []
    
    for func_name, stats in function_stats.items():
        if stats['call_count'] > 0:
            functions.append(func_name.split('.')[-1])  # 只显示函数名
            avg_times.append(stats['avg_time'] * 1000)  # 转换为毫秒
            call_counts.append(stats['call_count'])
    
    if not functions:
        return go.Figure().add_annotation(
            text="暂无函数调用数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False
        )
    
    # 创建气泡图
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=call_counts,
        y=avg_times,
        mode='markers',
        marker=dict(
            size=[min(50, max(10, count/10)) for count in call_counts],
            color=avg_times,
            colorscale='Viridis',
            showscale=True,
            colorbar=dict(title="平均执行时间(ms)")
        ),
        text=functions,
        textposition="middle center",
        hovertemplate="<b>%{text}</b><br>" +
                      "调用次数: %{x}<br>" +
                      "平均时间: %{y:.2f}ms<br>" +
                      "<extra></extra>"
    ))
    
    fig.update_layout(
        title="函数性能分析 (气泡大小表示调用频率)",
        xaxis_title="调用次数",
        yaxis_title="平均执行时间 (毫秒)",
        height=400
    )
    
    return fig


def create_recommendations_list(recommendations):
    """创建优化建议列表"""
    if not recommendations:
        return html.P("暂无优化建议", className="text-muted")
    
    items = []
    for i, rec in enumerate(recommendations):
        color = "success" if "良好" in rec else "warning"
        icon = "fas fa-check" if "良好" in rec else "fas fa-exclamation-triangle"
        
        items.append(
            dbc.ListGroupItem([
                html.I(className=f"{icon} me-2"),
                rec
            ], color=color)
        )
    
    return dbc.ListGroup(items, flush=True)


def create_detailed_stats_table(function_stats):
    """创建详细统计表格"""
    if not function_stats:
        return html.P("暂无详细统计数据", className="text-muted")
    
    # 准备表格数据
    rows = []
    for func_name, stats in function_stats.items():
        if stats['call_count'] > 0:
            rows.append(html.Tr([
                html.Td(func_name.split('.')[-1]),
                html.Td(f"{stats['call_count']:,}"),
                html.Td(f"{stats['avg_time']*1000:.2f}ms"),
                html.Td(f"{stats['min_time']*1000:.2f}ms"),
                html.Td(f"{stats['max_time']*1000:.2f}ms"),
                html.Td(f"{stats['total_time']:.2f}s")
            ]))
    
    if not rows:
        return html.P("暂无函数调用数据", className="text-muted")
    
    table = dbc.Table([
        html.Thead([
            html.Tr([
                html.Th("函数名"),
                html.Th("调用次数"),
                html.Th("平均时间"),
                html.Th("最短时间"),
                html.Th("最长时间"),
                html.Th("总时间")
            ])
        ]),
        html.Tbody(rows)
    ], striped=True, hover=True, size="sm")
    
    return table


def create_performance_alerts(system_metrics, cache_metrics):
    """创建性能警告"""
    alerts = []
    
    # CPU警告
    cpu_percent = system_metrics.get('cpu_percent', 0)
    if cpu_percent > 80:
        alerts.append(
            dbc.Alert([
                html.I(className="fas fa-exclamation-triangle me-2"),
                f"CPU使用率过高: {cpu_percent:.1f}%"
            ], color="danger", dismissable=True)
        )
    
    # 内存警告
    memory_percent = system_metrics.get('memory_percent', 0)
    if memory_percent > 85:
        alerts.append(
            dbc.Alert([
                html.I(className="fas fa-exclamation-triangle me-2"),
                f"内存使用率过高: {memory_percent:.1f}%"
            ], color="warning", dismissable=True)
        )
    
    return html.Div(alerts)


@callback(
    Output('performance-alerts', 'children', allow_duplicate=True),
    Input('clear-cache', 'n_clicks'),
    prevent_initial_call=True
)
def clear_cache_callback(n_clicks):
    """清理缓存回调"""
    if n_clicks:
        clear_cache()
        return dbc.Alert([
            html.I(className="fas fa-check me-2"),
            "缓存已清理"
        ], color="success", dismissable=True, duration=3000)
    return []


@callback(
    Output('performance-alerts', 'children', allow_duplicate=True),
    Input('clear-stats', 'n_clicks'),
    prevent_initial_call=True
)
def clear_stats_callback(n_clicks):
    """重置统计回调"""
    if n_clicks:
        clear_performance_stats()
        return dbc.Alert([
            html.I(className="fas fa-check me-2"),
            "性能统计已重置"
        ], color="success", dismissable=True, duration=3000)
    return []
