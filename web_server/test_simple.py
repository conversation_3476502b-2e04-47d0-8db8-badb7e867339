"""
简化的应用测试套件
测试应用的核心功能
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import tempfile

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入要测试的模块
try:
    from data_handler import DataHandler
    from error_handler import validate_data_input, DataLoadError, ValidationError
    from performance_monitor import PerformanceMonitor
    from cache_manager import CacheManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class TestBasicFunctionality(unittest.TestCase):
    """测试基本功能"""
    
    def test_data_handler_creation(self):
        """测试数据处理器创建"""
        data_handler = DataHandler()
        self.assertIsNotNone(data_handler)
    
    def test_cache_manager_creation(self):
        """测试缓存管理器创建"""
        cache_manager = CacheManager()
        self.assertIsNotNone(cache_manager)
    
    def test_performance_monitor_creation(self):
        """测试性能监控器创建"""
        monitor = PerformanceMonitor()
        self.assertIsNotNone(monitor)
    
    def test_error_validation(self):
        """测试错误验证"""
        # 测试正常情况
        try:
            validate_data_input("test_value", "测试字段", required=True)
        except ValidationError:
            self.fail("正常数据验证失败")
        
        # 测试必填字段为空
        with self.assertRaises(ValidationError):
            validate_data_input("", "测试字段", required=True)
    
    def test_data_load_error(self):
        """测试数据加载错误"""
        error = DataLoadError("文件不存在", "/path/to/file")
        
        self.assertEqual(error.message, "文件不存在")
        self.assertEqual(error.file_path, "/path/to/file")
        self.assertEqual(error.error_code, "DATA_LOAD_ERROR")
    
    def test_cache_basic_operations(self):
        """测试缓存基本操作"""
        cache_manager = CacheManager()
        
        # 测试设置和获取
        test_data = {'key': 'value', 'number': 123}
        cache_manager.set('test_key', test_data, timeout=60)
        result = cache_manager.get('test_key')
        self.assertEqual(result, test_data)
        
        # 测试不存在的键
        result = cache_manager.get('nonexistent_key')
        self.assertIsNone(result)
    
    def test_performance_monitor_basic(self):
        """测试性能监控基本功能"""
        monitor = PerformanceMonitor()
        
        # 测试记录指标
        monitor.record_metric('test_metric', 100)
        self.assertIn('test_metric', monitor.metrics)
        
        # 测试记录函数调用
        monitor.record_function_call('test_function', 0.5)
        stats = monitor.get_function_stats('test_function')
        self.assertEqual(stats['call_count'], 1)
        self.assertEqual(stats['total_time'], 0.5)
    
    @patch('os.path.exists')
    def test_data_handler_path_resolution(self, mock_exists):
        """测试数据处理器路径解析"""
        mock_exists.return_value = True
        data_handler = DataHandler()
        
        # 测试管理员用户路径
        admin_path = data_handler.get_real_path('admin', '/test/path', 'strategy1')
        self.assertIn('strategy1', admin_path)
        
        # 测试普通用户路径
        user_path = data_handler.get_real_path('user1', '/jupyter/test', 'strategy1')
        self.assertIn('user1', user_path)
    
    def test_module_imports(self):
        """测试模块导入"""
        # 测试所有主要模块都能正常导入
        modules_to_test = [
            'config',
            'data_handler', 
            'chart_generator',
            'layout',
            'callbacks',
            'cache_manager',
            'error_handler',
            'performance_monitor'
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                print(f"✅ 模块 {module_name} 导入成功")
            except ImportError as e:
                print(f"❌ 模块 {module_name} 导入失败: {e}")
                # 不让测试失败，只是记录
    
    def test_config_loading(self):
        """测试配置加载"""
        try:
            import config
            # 检查主要配置是否存在
            self.assertTrue(hasattr(config, 'UI_CONFIG'))
            self.assertTrue(hasattr(config, 'THEME_CONFIG'))
            print("✅ 配置文件加载成功")
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")


class TestApplicationIntegration(unittest.TestCase):
    """测试应用集成"""
    
    def test_app_startup(self):
        """测试应用启动组件"""
        try:
            # 测试布局创建
            from layout import create_layout
            layout = create_layout()
            self.assertIsNotNone(layout)
            print("✅ 布局创建成功")
            
            # 测试回调函数导入
            import callbacks
            print("✅ 回调函数导入成功")
            
        except Exception as e:
            print(f"❌ 应用组件测试失败: {e}")
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        try:
            from error_handler import handle_errors, create_error_display
            
            # 测试错误装饰器
            @handle_errors(return_on_error="default_value")
            def test_function():
                raise ValueError("测试错误")
            
            result = test_function()
            self.assertEqual(result, "default_value")
            print("✅ 错误处理装饰器工作正常")
            
            # 测试错误显示组件
            error_component = create_error_display("测试错误", "TEST_ERROR")
            self.assertIsNotNone(error_component)
            print("✅ 错误显示组件创建成功")
            
        except Exception as e:
            print(f"❌ 错误处理集成测试失败: {e}")


def run_simple_tests():
    """运行简化测试"""
    print("开始运行简化测试套件...")
    print("=" * 50)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestBasicFunctionality,
        TestApplicationIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 50)
    print(f"测试完成!")
    print(f"总测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"成功率: {success_rate:.1f}%")
    else:
        print("成功率: 0%")
    
    if result.wasSuccessful():
        print("✅ 所有测试通过!")
        return True
    else:
        print("❌ 部分测试失败")
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        return False


if __name__ == '__main__':
    success = run_simple_tests()
    sys.exit(0 if success else 1)
