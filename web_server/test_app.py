"""
应用测试套件
测试应用的核心功能和组件
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import tempfile
import json

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入要测试的模块
from data_handler import DataHandler
from chart_generator import ChartGenerator
from cache_manager import CacheManager
from error_handler import (
    validate_data_input, DataLoadError, ValidationError,
    create_error_display, create_loading_component
)
from performance_monitor import PerformanceMonitor


class TestDataHandler(unittest.TestCase):
    """测试数据处理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.data_handler = DataHandler()
        
        # 创建测试数据
        self.test_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            '策略净值': np.random.randn(100).cumsum() + 100,
            '基准指数': np.random.randn(100).cumsum() + 100,
            '收益率': np.random.randn(100) * 0.02
        })
    
    def test_get_real_path(self):
        """测试路径解析"""
        # 测试管理员用户
        admin_path = self.data_handler.get_real_path('admin', '/test/path', 'strategy1')
        self.assertIn('strategy1', admin_path)
        
        # 测试普通用户
        user_path = self.data_handler.get_real_path('user1', '/jupyter/test', 'strategy1')
        self.assertIn('user1', user_path)
    
    @patch('os.path.exists')
    @patch('os.listdir')
    def test_get_strategy_options(self, mock_listdir, mock_exists):
        """测试策略选项获取"""
        mock_exists.return_value = True
        mock_listdir.return_value = ['strategy1', 'strategy2', '.hidden']
        
        options = self.data_handler.get_strategy_options('admin', '/test/path')
        
        self.assertEqual(len(options), 2)
        self.assertEqual(options[0]['label'], 'strategy1')
        self.assertEqual(options[1]['label'], 'strategy2')
    
    @patch('pandas.read_feather')
    @patch('os.path.exists')
    def test_load_daily_return_data(self, mock_exists, mock_read_feather):
        """测试日收益数据加载"""
        mock_exists.return_value = True
        mock_read_feather.return_value = self.test_data
        
        result = self.data_handler.load_daily_return_data('admin', '/test', 'strategy1', 'account1')
        
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 100)
        mock_read_feather.assert_called_once()
    
    def test_period_filter(self):
        """测试周期过滤"""
        # 测试最近一年
        filtered_data = self.data_handler.period_filter(self.test_data, '最近一年')
        self.assertLessEqual(len(filtered_data), len(self.test_data))
        
        # 测试全部数据
        all_data = self.data_handler.period_filter(self.test_data, '全部')
        self.assertEqual(len(all_data), len(self.test_data))


class TestChartGenerator(unittest.TestCase):
    """测试图表生成器"""
    
    def setUp(self):
        """设置测试环境"""
        self.chart_generator = ChartGenerator()
        
        # 创建测试数据
        self.test_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=50),
            '策略净值': np.random.randn(50).cumsum() + 100,
            '基准指数': np.random.randn(50).cumsum() + 100,
            '收益率': np.random.randn(50) * 0.02
        })
    
    def test_create_main_chart(self):
        """测试主图表创建"""
        fig = self.chart_generator.create_main_chart(self.test_data, 'test_account')
        
        self.assertIsNotNone(fig)
        self.assertTrue(hasattr(fig, 'data'))
        self.assertGreater(len(fig.data), 0)
    
    def test_create_metrics_chart(self):
        """测试指标图表创建"""
        metrics = ['收益率', '波动率']
        fig = self.chart_generator.create_metrics_chart(self.test_data, metrics, 'test_account')
        
        self.assertIsNotNone(fig)
        self.assertTrue(hasattr(fig, 'data'))
    
    def test_get_color_palette(self):
        """测试颜色调色板"""
        # 测试颜色配置是否存在
        self.assertTrue(hasattr(self.chart_generator, 'colors'))
        self.assertIsInstance(self.chart_generator.colors, dict)


class TestCacheManager(unittest.TestCase):
    """测试缓存管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.cache_manager = CacheManager()
    
    def test_set_and_get(self):
        """测试缓存设置和获取"""
        test_data = {'key': 'value', 'number': 123}
        
        # 设置缓存
        self.cache_manager.set('test_key', test_data, timeout=60)
        
        # 获取缓存
        result = self.cache_manager.get('test_key')
        
        self.assertEqual(result, test_data)
    
    def test_cache_expiration(self):
        """测试缓存过期"""
        test_data = 'test_value'
        
        # 设置短期缓存
        self.cache_manager.set('expire_key', test_data, timeout=0.1)
        
        # 立即获取应该成功
        result = self.cache_manager.get('expire_key')
        self.assertEqual(result, test_data)
        
        # 等待过期后获取应该返回None
        import time
        time.sleep(0.2)
        result = self.cache_manager.get('expire_key')
        self.assertIsNone(result)
    
    def test_cache_decorator(self):
        """测试缓存装饰器"""
        from cache_manager import cached
        call_count = 0

        @cached(timeout=60, key_prefix='test_func')
        def test_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y

        # 第一次调用
        result1 = test_function(1, 2)
        self.assertEqual(result1, 3)
        self.assertEqual(call_count, 1)

        # 第二次调用应该使用缓存
        result2 = test_function(1, 2)
        self.assertEqual(result2, 3)
        self.assertEqual(call_count, 1)  # 调用次数不应该增加


class TestErrorHandler(unittest.TestCase):
    """测试错误处理器"""
    
    def test_validate_data_input(self):
        """测试数据验证"""
        # 测试正常情况
        try:
            validate_data_input("test_value", "测试字段", required=True)
        except ValidationError:
            self.fail("正常数据验证失败")
        
        # 测试必填字段为空
        with self.assertRaises(ValidationError):
            validate_data_input("", "测试字段", required=True)
        
        # 测试类型验证
        with self.assertRaises(ValidationError):
            validate_data_input("string", "数字字段", data_type=int)
    
    def test_data_load_error(self):
        """测试数据加载错误"""
        error = DataLoadError("文件不存在", "/path/to/file")
        
        self.assertEqual(error.message, "文件不存在")
        self.assertEqual(error.file_path, "/path/to/file")
        self.assertEqual(error.error_code, "DATA_LOAD_ERROR")
    
    def test_create_error_display(self):
        """测试错误显示组件创建"""
        error_component = create_error_display("测试错误", "TEST_ERROR")
        
        self.assertIsNotNone(error_component)
        # 检查是否包含错误信息
        self.assertTrue(hasattr(error_component, 'children'))
    
    def test_create_loading_component(self):
        """测试加载组件创建"""
        loading_component = create_loading_component("加载中...")
        
        self.assertIsNotNone(loading_component)
        self.assertTrue(hasattr(loading_component, 'children'))


class TestPerformanceMonitor(unittest.TestCase):
    """测试性能监控器"""
    
    def setUp(self):
        """设置测试环境"""
        self.monitor = PerformanceMonitor(max_records=100)
    
    def test_record_metric(self):
        """测试指标记录"""
        self.monitor.record_metric('cpu_usage', 75.5)
        
        metrics = self.monitor.metrics
        self.assertIn('cpu_usage', metrics)
        self.assertEqual(len(metrics['cpu_usage']), 1)
        self.assertEqual(metrics['cpu_usage'][0]['value'], 75.5)
    
    def test_record_function_call(self):
        """测试函数调用记录"""
        self.monitor.record_function_call('test_function', 0.5)
        
        stats = self.monitor.get_function_stats('test_function')
        self.assertEqual(stats['call_count'], 1)
        self.assertEqual(stats['total_time'], 0.5)
        self.assertEqual(stats['avg_time'], 0.5)
    
    def test_performance_summary(self):
        """测试性能摘要"""
        # 记录一些测试数据
        self.monitor.record_function_call('func1', 0.1)
        self.monitor.record_function_call('func2', 0.2)
        
        summary = self.monitor.get_performance_summary()
        
        self.assertIn('uptime_seconds', summary)
        self.assertIn('slowest_functions', summary)
        self.assertIn('total_function_calls', summary)
        self.assertEqual(summary['total_function_calls'], 2)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.data_handler = DataHandler()
        self.chart_generator = ChartGenerator()
    
    @patch('pandas.read_feather')
    @patch('os.path.exists')
    def test_data_to_chart_pipeline(self, mock_exists, mock_read_feather):
        """测试数据到图表的完整流程"""
        # 模拟数据加载
        mock_exists.return_value = True
        test_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=30),
            '策略净值': np.random.randn(30).cumsum() + 100,
            '基准指数': np.random.randn(30).cumsum() + 100
        })
        mock_read_feather.return_value = test_data
        
        # 加载数据
        data = self.data_handler.load_daily_return_data('admin', '/test', 'strategy1', 'account1')
        self.assertIsNotNone(data)
        
        # 生成图表
        fig = self.chart_generator.create_main_chart(data, 'account1')
        self.assertIsNotNone(fig)
        self.assertGreater(len(fig.data), 0)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDataHandler,
        TestChartGenerator,
        TestCacheManager,
        TestErrorHandler,
        TestPerformanceMonitor,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 返回测试结果
    return result.wasSuccessful(), result.testsRun, len(result.failures), len(result.errors)


if __name__ == '__main__':
    print("开始运行应用测试套件...")
    print("=" * 50)
    
    success, total, failures, errors = run_tests()
    
    print("=" * 50)
    print(f"测试完成!")
    print(f"总测试数: {total}")
    print(f"失败数: {failures}")
    print(f"错误数: {errors}")
    print(f"成功率: {((total - failures - errors) / total * 100):.1f}%" if total > 0 else "0%")
    
    if success:
        print("✅ 所有测试通过!")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    sys.exit(0 if success else 1)
