"""
部署配置文件
包含生产环境和开发环境的配置
"""

import os
from typing import Dict, Any


class DeployConfig:
    """部署配置类"""
    
    def __init__(self, environment: str = 'development'):
        self.environment = environment
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        base_config = {
            'app_name': 'Financial Strategy Backtesting Dashboard',
            'version': '2.0.0',
            'debug': False,
            'host': '0.0.0.0',
            'port': 8051,
            'workers': 1,
            'timeout': 300,
            'max_content_length': 100 * 1024 * 1024,  # 100MB
            'cache_config': {
                'type': 'memory',
                'max_entries': 1000,
                'default_timeout': 300
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'app.log',
                'max_bytes': 10 * 1024 * 1024,  # 10MB
                'backup_count': 5
            },
            'security': {
                'secret_key': os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production'),
                'csrf_enabled': True,
                'session_timeout': 3600  # 1 hour
            },
            'performance': {
                'enable_monitoring': True,
                'max_function_records': 1000,
                'slow_query_threshold': 1.0  # seconds
            }
        }
        
        if self.environment == 'development':
            base_config.update({
                'debug': True,
                'host': '127.0.0.1',
                'logging': {
                    **base_config['logging'],
                    'level': 'DEBUG'
                }
            })
        elif self.environment == 'production':
            base_config.update({
                'debug': False,
                'workers': 4,
                'host': '0.0.0.0',
                'port': int(os.environ.get('PORT', 8051)),
                'cache_config': {
                    'type': 'redis',
                    'redis_url': os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
                    'max_entries': 10000,
                    'default_timeout': 600
                },
                'logging': {
                    **base_config['logging'],
                    'level': 'WARNING'
                },
                'security': {
                    **base_config['security'],
                    'secret_key': os.environ.get('SECRET_KEY'),
                    'csrf_enabled': True
                }
            })
        elif self.environment == 'testing':
            base_config.update({
                'debug': True,
                'host': '127.0.0.1',
                'port': 8052,
                'cache_config': {
                    'type': 'memory',
                    'max_entries': 100,
                    'default_timeout': 60
                },
                'logging': {
                    **base_config['logging'],
                    'level': 'DEBUG'
                }
            })
        
        return base_config
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def get_nested(self, *keys: str, default: Any = None) -> Any:
        """获取嵌套配置值"""
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value


def get_deploy_config(environment: str = None) -> DeployConfig:
    """获取部署配置"""
    if environment is None:
        environment = os.environ.get('FLASK_ENV', 'development')
    
    return DeployConfig(environment)


def setup_logging(config: DeployConfig):
    """设置日志配置"""
    import logging
    from logging.handlers import RotatingFileHandler
    
    # 获取日志配置
    log_config = config.get('logging', {})
    
    # 设置日志级别
    level = getattr(logging, log_config.get('level', 'INFO'))
    
    # 创建格式化器
    formatter = logging.Formatter(log_config.get('format'))
    
    # 设置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 添加文件处理器（如果配置了文件）
    if log_config.get('file'):
        file_handler = RotatingFileHandler(
            log_config['file'],
            maxBytes=log_config.get('max_bytes', 10 * 1024 * 1024),
            backupCount=log_config.get('backup_count', 5)
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)


def validate_environment():
    """验证环境配置"""
    required_env_vars = []
    missing_vars = []
    
    environment = os.environ.get('FLASK_ENV', 'development')
    
    if environment == 'production':
        required_env_vars = ['SECRET_KEY']
        
        # 检查Redis配置（如果使用Redis缓存）
        if os.environ.get('CACHE_TYPE') == 'redis':
            required_env_vars.append('REDIS_URL')
    
    for var in required_env_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise EnvironmentError(
            f"缺少必需的环境变量: {', '.join(missing_vars)}"
        )
    
    return True


def create_app_with_config(environment: str = None):
    """使用配置创建应用"""
    # 验证环境
    validate_environment()
    
    # 获取配置
    config = get_deploy_config(environment)
    
    # 设置日志
    setup_logging(config)
    
    # 导入应用
    from app_new import app
    
    # 应用配置
    app.config.update({
        'SECRET_KEY': config.get_nested('security', 'secret_key'),
        'DEBUG': config.get('debug', False),
        'MAX_CONTENT_LENGTH': config.get('max_content_length')
    })
    
    return app, config


def get_server_config(config: DeployConfig) -> Dict[str, Any]:
    """获取服务器配置"""
    return {
        'host': config.get('host'),
        'port': config.get('port'),
        'debug': config.get('debug'),
        'threaded': True,
        'use_reloader': config.get('debug', False)
    }


# 环境检查函数
def check_dependencies():
    """检查依赖项"""
    required_packages = [
        'dash', 'plotly', 'pandas', 'numpy', 
        'dash_bootstrap_components', 'redis'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"警告: 缺少以下包: {', '.join(missing_packages)}")
        return False
    
    return True


def check_data_directories(config: DeployConfig):
    """检查数据目录"""
    # 这里可以添加数据目录检查逻辑
    # 例如检查数据文件是否存在，权限是否正确等
    pass


def health_check():
    """健康检查"""
    checks = {
        'dependencies': check_dependencies(),
        'environment': True,
        'config': True
    }
    
    try:
        validate_environment()
    except EnvironmentError:
        checks['environment'] = False
    
    try:
        config = get_deploy_config()
        check_data_directories(config)
    except Exception:
        checks['config'] = False
    
    all_passed = all(checks.values())
    
    return {
        'status': 'healthy' if all_passed else 'unhealthy',
        'checks': checks,
        'timestamp': __import__('datetime').datetime.now().isoformat()
    }


if __name__ == '__main__':
    # 运行健康检查
    result = health_check()
    print("健康检查结果:")
    print(f"状态: {result['status']}")
    print("检查项目:")
    for check, status in result['checks'].items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {check}")
    
    if result['status'] == 'healthy':
        print("\n✅ 应用准备就绪，可以部署!")
    else:
        print("\n❌ 应用未准备就绪，请检查失败的项目")
