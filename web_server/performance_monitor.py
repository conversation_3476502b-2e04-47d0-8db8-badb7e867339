"""
性能监控模块
监控应用性能指标，提供性能分析和优化建议
"""

import time
import psutil
import threading
from typing import Dict, List, Optional
from functools import wraps
from collections import defaultdict, deque
from cache_manager import get_cache_stats


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.metrics = defaultdict(lambda: deque(maxlen=max_records))
        self.function_stats = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0,
            'avg_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'recent_times': deque(maxlen=100)
        })
        self.lock = threading.Lock()
        self.start_time = time.time()
    
    def record_metric(self, name: str, value: float, timestamp: Optional[float] = None):
        """记录性能指标"""
        if timestamp is None:
            timestamp = time.time()
        
        with self.lock:
            self.metrics[name].append({
                'value': value,
                'timestamp': timestamp
            })
    
    def record_function_call(self, func_name: str, execution_time: float):
        """记录函数调用性能"""
        with self.lock:
            stats = self.function_stats[func_name]
            stats['call_count'] += 1
            stats['total_time'] += execution_time
            stats['avg_time'] = stats['total_time'] / stats['call_count']
            stats['min_time'] = min(stats['min_time'], execution_time)
            stats['max_time'] = max(stats['max_time'], execution_time)
            stats['recent_times'].append(execution_time)
    
    def get_system_metrics(self) -> Dict:
        """获取系统性能指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': memory.used / (1024**3),
                'memory_total_gb': memory.total / (1024**3),
                'disk_percent': disk.percent,
                'disk_used_gb': disk.used / (1024**3),
                'disk_total_gb': disk.total / (1024**3),
                'timestamp': time.time()
            }
        except Exception as e:
            print(f"Error getting system metrics: {e}")
            return {}
    
    def get_cache_metrics(self) -> Dict:
        """获取缓存性能指标"""
        try:
            return get_cache_stats()
        except Exception as e:
            print(f"Error getting cache metrics: {e}")
            return {}
    
    def get_function_stats(self, func_name: Optional[str] = None) -> Dict:
        """获取函数性能统计"""
        with self.lock:
            if func_name:
                return dict(self.function_stats.get(func_name, {}))
            else:
                return {name: dict(stats) for name, stats in self.function_stats.items()}
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        system_metrics = self.get_system_metrics()
        cache_metrics = self.get_cache_metrics()
        
        # 计算应用运行时间
        uptime = time.time() - self.start_time
        
        # 获取最慢的函数
        slowest_functions = []
        with self.lock:
            for func_name, stats in self.function_stats.items():
                if stats['call_count'] > 0:
                    slowest_functions.append({
                        'name': func_name,
                        'avg_time': stats['avg_time'],
                        'max_time': stats['max_time'],
                        'call_count': stats['call_count']
                    })
        
        slowest_functions.sort(key=lambda x: x['avg_time'], reverse=True)
        
        return {
            'uptime_seconds': uptime,
            'system_metrics': system_metrics,
            'cache_metrics': cache_metrics,
            'slowest_functions': slowest_functions[:10],
            'total_function_calls': sum(stats['call_count'] for stats in self.function_stats.values()),
            'timestamp': time.time()
        }
    
    def get_performance_recommendations(self) -> List[str]:
        """获取性能优化建议"""
        recommendations = []
        
        try:
            system_metrics = self.get_system_metrics()
            cache_metrics = self.get_cache_metrics()
            
            # CPU使用率建议
            if system_metrics.get('cpu_percent', 0) > 80:
                recommendations.append("CPU使用率过高，考虑优化计算密集型操作或增加缓存")
            
            # 内存使用建议
            if system_metrics.get('memory_percent', 0) > 85:
                recommendations.append("内存使用率过高，考虑优化数据结构或增加内存")
            
            # 缓存命中率建议
            if isinstance(cache_metrics, dict) and 'total_entries' in cache_metrics:
                if cache_metrics.get('total_entries', 0) > cache_metrics.get('max_entries', 1000) * 0.9:
                    recommendations.append("缓存接近容量上限，考虑增加缓存大小或优化缓存策略")
            
            # 函数性能建议
            with self.lock:
                for func_name, stats in self.function_stats.items():
                    if stats['avg_time'] > 1.0:  # 平均执行时间超过1秒
                        recommendations.append(f"函数 {func_name} 执行时间较长({stats['avg_time']:.2f}s)，考虑优化")
                    
                    if stats['call_count'] > 100 and stats['avg_time'] > 0.1:
                        recommendations.append(f"函数 {func_name} 调用频繁且耗时，建议添加缓存")
            
            if not recommendations:
                recommendations.append("系统性能良好，无需特别优化")
                
        except Exception as e:
            recommendations.append(f"性能分析出错: {e}")
        
        return recommendations
    
    def clear_stats(self):
        """清空统计数据"""
        with self.lock:
            self.metrics.clear()
            self.function_stats.clear()
            self.start_time = time.time()


def monitor_performance(func_name: Optional[str] = None):
    """性能监控装饰器"""
    def decorator(func):
        name = func_name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                execution_time = time.time() - start_time
                performance_monitor.record_function_call(name, execution_time)
        
        return wrapper
    return decorator


class PerformanceMiddleware:
    """性能监控中间件"""
    
    def __init__(self, app):
        self.app = app
        self.setup_monitoring()
    
    def setup_monitoring(self):
        """设置监控"""
        # 定期记录系统指标
        def record_system_metrics():
            while True:
                try:
                    metrics = performance_monitor.get_system_metrics()
                    if metrics:
                        performance_monitor.record_metric('cpu_percent', metrics['cpu_percent'])
                        performance_monitor.record_metric('memory_percent', metrics['memory_percent'])
                        performance_monitor.record_metric('disk_percent', metrics['disk_percent'])
                except Exception as e:
                    print(f"Error recording system metrics: {e}")
                
                time.sleep(30)  # 每30秒记录一次
        
        # 启动后台线程
        thread = threading.Thread(target=record_system_metrics, daemon=True)
        thread.start()


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def get_performance_dashboard() -> Dict:
    """获取性能仪表板数据"""
    return {
        'summary': performance_monitor.get_performance_summary(),
        'recommendations': performance_monitor.get_performance_recommendations(),
        'function_stats': performance_monitor.get_function_stats()
    }


def clear_performance_stats():
    """清空性能统计"""
    performance_monitor.clear_stats()


# 为关键函数添加性能监控
def setup_performance_monitoring():
    """设置性能监控"""
    # 这里可以为关键模块的函数添加监控装饰器
    pass
