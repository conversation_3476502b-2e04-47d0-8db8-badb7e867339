"""
错误处理模块
提供统一的错误处理和用户友好的错误信息显示
"""

import traceback
import logging
from typing import Any, Dict, Optional, Tuple
from functools import wraps
from dash import html
import dash_bootstrap_components as dbc


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class AppError(Exception):
    """应用自定义异常基类"""
    
    def __init__(self, message: str, error_code: str = "UNKNOWN", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DataLoadError(AppError):
    """数据加载错误"""
    
    def __init__(self, message: str, file_path: str = "", details: Optional[Dict] = None):
        super().__init__(message, "DATA_LOAD_ERROR", details)
        self.file_path = file_path


class ValidationError(AppError):
    """数据验证错误"""
    
    def __init__(self, message: str, field: str = "", details: Optional[Dict] = None):
        super().__init__(message, "VALIDATION_ERROR", details)
        self.field = field


class ConfigurationError(AppError):
    """配置错误"""
    
    def __init__(self, message: str, config_key: str = "", details: Optional[Dict] = None):
        super().__init__(message, "CONFIG_ERROR", details)
        self.config_key = config_key


def handle_errors(return_on_error=None, log_errors=True):
    """错误处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except AppError as e:
                if log_errors:
                    logger.error(f"应用错误在 {func.__name__}: {e.message}", 
                               extra={'error_code': e.error_code, 'details': e.details})
                
                if return_on_error is not None:
                    return return_on_error
                
                # 返回用户友好的错误信息
                return create_error_display(e.message, e.error_code)
                
            except Exception as e:
                if log_errors:
                    logger.error(f"未处理的错误在 {func.__name__}: {str(e)}", 
                               exc_info=True)
                
                if return_on_error is not None:
                    return return_on_error
                
                # 返回通用错误信息
                return create_error_display("系统出现未知错误，请稍后重试", "SYSTEM_ERROR")
        
        return wrapper
    return decorator


def create_error_display(message: str, error_code: str = "ERROR", 
                        show_details: bool = False, details: Optional[str] = None) -> html.Div:
    """创建错误显示组件"""
    
    # 根据错误类型选择颜色和图标
    error_config = {
        "DATA_LOAD_ERROR": {"color": "warning", "icon": "fas fa-exclamation-triangle"},
        "VALIDATION_ERROR": {"color": "danger", "icon": "fas fa-times-circle"},
        "CONFIG_ERROR": {"color": "info", "icon": "fas fa-cog"},
        "SYSTEM_ERROR": {"color": "danger", "icon": "fas fa-bug"},
        "NETWORK_ERROR": {"color": "warning", "icon": "fas fa-wifi"},
        "PERMISSION_ERROR": {"color": "danger", "icon": "fas fa-lock"}
    }
    
    config = error_config.get(error_code, {"color": "secondary", "icon": "fas fa-question-circle"})
    
    alert_content = [
        html.Div([
            html.I(className=f"{config['icon']} me-2"),
            html.Strong("错误: "),
            html.Span(message)
        ])
    ]
    
    if show_details and details:
        alert_content.append(
            html.Details([
                html.Summary("查看详细信息", className="mt-2"),
                html.Pre(details, className="mt-2 small text-muted")
            ])
        )
    
    return dbc.Alert(
        alert_content,
        color=config["color"],
        dismissable=True,
        className="mb-3"
    )


def create_loading_component(loading_text: str = "加载中...", 
                           loading_type: str = "default") -> html.Div:
    """创建加载组件"""
    
    loading_configs = {
        "default": {"type": "default", "color": "#1f77b4"},
        "cube": {"type": "cube", "color": "#0d6efd"},
        "circle": {"type": "circle", "color": "#198754"},
        "dot": {"type": "dot", "color": "#fd7e14"},
        "graph": {"type": "graph", "color": "#6f42c1"}
    }
    
    config = loading_configs.get(loading_type, loading_configs["default"])
    
    return html.Div([
        html.Div([
            html.I(className="fas fa-spinner fa-spin me-2"),
            html.Span(loading_text)
        ], className="text-center text-muted p-4")
    ], className="d-flex justify-content-center align-items-center", 
       style={"minHeight": "200px"})


def create_empty_state(title: str = "暂无数据", 
                      description: str = "请检查数据源或重新加载",
                      icon: str = "fas fa-inbox",
                      action_button: Optional[Dict] = None) -> html.Div:
    """创建空状态组件"""
    
    content = [
        html.Div([
            html.I(className=f"{icon} fa-3x text-muted mb-3"),
            html.H4(title, className="text-muted"),
            html.P(description, className="text-muted")
        ], className="text-center py-5")
    ]
    
    if action_button:
        content[0].children.append(
            dbc.Button(
                action_button.get("text", "重新加载"),
                id=action_button.get("id", "reload-button"),
                color=action_button.get("color", "primary"),
                size="sm",
                className="mt-3"
            )
        )
    
    return html.Div(content)


def validate_data_input(data: Any, field_name: str, required: bool = True, 
                       data_type: type = None, min_length: int = None) -> None:
    """验证数据输入"""
    
    if required and (data is None or data == ""):
        raise ValidationError(f"{field_name}不能为空", field_name)
    
    if data is not None and data_type is not None:
        if not isinstance(data, data_type):
            raise ValidationError(f"{field_name}类型错误，期望{data_type.__name__}", field_name)
    
    if min_length is not None and hasattr(data, '__len__'):
        if len(data) < min_length:
            raise ValidationError(f"{field_name}长度不能少于{min_length}", field_name)


def safe_file_operation(operation_func, file_path: str, operation_name: str = "文件操作"):
    """安全的文件操作"""
    try:
        return operation_func()
    except FileNotFoundError:
        raise DataLoadError(f"文件不存在: {file_path}", file_path)
    except PermissionError:
        raise DataLoadError(f"没有权限访问文件: {file_path}", file_path)
    except Exception as e:
        raise DataLoadError(f"{operation_name}失败: {str(e)}", file_path)


def create_retry_component(retry_func_id: str, retry_text: str = "重试") -> html.Div:
    """创建重试组件"""
    return html.Div([
        dbc.Button([
            html.I(className="fas fa-redo me-1"),
            retry_text
        ], id=retry_func_id, color="outline-primary", size="sm")
    ], className="text-center mt-3")


def log_user_action(action: str, details: Optional[Dict] = None):
    """记录用户操作"""
    logger.info(f"用户操作: {action}", extra={'details': details or {}})


def create_success_message(message: str, auto_dismiss: bool = True) -> dbc.Alert:
    """创建成功消息"""
    return dbc.Alert([
        html.I(className="fas fa-check-circle me-2"),
        message
    ], color="success", dismissable=True, 
       duration=3000 if auto_dismiss else None)


def create_warning_message(message: str, auto_dismiss: bool = False) -> dbc.Alert:
    """创建警告消息"""
    return dbc.Alert([
        html.I(className="fas fa-exclamation-triangle me-2"),
        message
    ], color="warning", dismissable=True,
       duration=5000 if auto_dismiss else None)


def create_info_message(message: str, auto_dismiss: bool = True) -> dbc.Alert:
    """创建信息消息"""
    return dbc.Alert([
        html.I(className="fas fa-info-circle me-2"),
        message
    ], color="info", dismissable=True,
       duration=4000 if auto_dismiss else None)


class ErrorBoundary:
    """错误边界类，用于捕获和处理组件级别的错误"""
    
    def __init__(self, component_name: str):
        self.component_name = component_name
    
    def wrap_component(self, component_func):
        """包装组件函数以提供错误边界"""
        @wraps(component_func)
        def wrapper(*args, **kwargs):
            try:
                return component_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"组件 {self.component_name} 渲染错误: {str(e)}", exc_info=True)
                return create_error_display(
                    f"组件 {self.component_name} 加载失败",
                    "COMPONENT_ERROR"
                )
        return wrapper


# 全局错误处理器实例
def setup_global_error_handler():
    """设置全局错误处理器"""
    import sys
    
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logger.critical("未捕获的异常", exc_info=(exc_type, exc_value, exc_traceback))
    
    sys.excepthook = handle_exception


# 初始化全局错误处理
setup_global_error_handler()
