"""
配置模块
包含应用的所有配置参数和常量
"""

# 应用配置
APP_CONFIG = {
    'host': '0.0.0.0',
    'port': 8051,
    'debug': False,
    'suppress_callback_exceptions': True
}

# 主题配置
THEME_CONFIG = {
    'external_stylesheets': ['https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css'],
    'external_scripts': ['https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js']
}

# 路径映射配置
PATH_MAPPINGS = {
    'nv_tracking': {
        'user': 'admin',
        'path': '/mnt/Data2/auto_task/nv_tracking_v2/'
    },
    'stock': {
        'user': 'admin', 
        'path': '/mnt/Data2/auto_task/nv_tracking_v3/gs_strage'
    },
    'fut': {
        'user': 'admin',
        'path': '/mnt/Data2/auto_task/nv_tracking_v3/fut_strage'
    }
}

# 指标配置
METRICS_CONFIG = {
    '股票': [
        {'label': '持仓标的数量', 'value': '持仓标的数量'},
        {'label': '成交金额', 'value': '成交金额'},
        {'label': '手续费', 'value': '手续费'},
        {'label': '持仓市值', 'value': '持仓市值'},
        {'label': '仓位占比', 'value': '仓位占比'},
        {'label': '交易次数', 'value': '交易次数'},
        {'label': '累计换手率', 'value': '累计换手率'},
    ],
    '期货': [
        {'label': '持仓标的数量', 'value': '持仓标的数量'},
        {'label': '成交金额', 'value': '成交金额'},
        {'label': '手续费', 'value': '手续费'},
        {'label': '保证金占用', 'value': '保证金占用'},
        {'label': '合约市值', 'value': '合约市值'},
        {'label': '仓位占比', 'value': '仓位占比'},
        {'label': '交易次数', 'value': '交易次数'},
    ],
    '全部账户汇总': [
        {'label': '持仓标的数量', 'value': '持仓标的数量'},
        {'label': '成交金额', 'value': '成交金额'},
        {'label': '手续费', 'value': '手续费'},
        {'label': '保证金占用', 'value': '保证金占用'},
        {'label': '持仓市值', 'value': '持仓市值'},
        {'label': '仓位占比', 'value': '仓位占比'},
        {'label': '交易次数', 'value': '交易次数'},
    ]
}

# 图表配置
CHART_CONFIG = {
    'colors': {
        'strategy': '#FF0000',
        'benchmark': '#93a5cf', 
        'excess': '#7046aa',
        'stock_account': '#fcc5e4',
        'futures_account': '#ff7882',
        'metrics': ['#FF0000', '#000000', '#FE00FF']
    },
    'heights': {
        'main_chart': 400,
        'sub_chart': 200
    },
    'layout': {
        'vertical_spacing': 0.1,
        'num_ticks': 10
    }
}

# 缓存配置
CACHE_CONFIG = {
    'type': 'memory',  # 'memory' 或 'redis'
    'redis_url': 'redis://localhost:6379/0',
    'default_timeout': 300,  # 5分钟
    'max_entries': 1000
}

# 文件配置
FILE_CONFIG = {
    'supported_formats': ['.fea', '.xlsx'],
    'data_folders': ['show_data', 'data', 'performance', 'run_info'],
    'config_keys': ['start_date', 'end_date', 'base_index', 'freq', 'price_mode', 'super_trader']
}

# UI配置
UI_CONFIG = {
    'page_title': '策略回测分析系统',
    'loading_type': 'cube',
    'table_page_size': 20,
    'chart_config': {
        'displayModeBar': True,
        'displaylogo': False,
        'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
        'toImageButtonOptions': {
            'format': 'png',
            'filename': 'strategy_chart',
            'height': 800,
            'width': 1200,
            'scale': 2
        }
    },
    'responsive_breakpoints': {
        'mobile': 576,
        'tablet': 768,
        'desktop': 992,
        'large': 1200
    },
    'animation': {
        'duration': 300,
        'easing': 'ease-out'
    }
}
