"""
缓存管理模块
提供内存缓存和Redis缓存支持，优化数据加载性能
"""

import hashlib
import json
import time
import pickle
from typing import Any, Optional, Dict, List
from functools import wraps
import pandas as pd

from config import CACHE_CONFIG


class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, max_entries: int = 1000, default_timeout: int = 300):
        self.cache: Dict[str, Dict] = {}
        self.max_entries = max_entries
        self.default_timeout = default_timeout
    
    def _is_expired(self, entry: Dict) -> bool:
        """检查缓存项是否过期"""
        if entry['timeout'] == 0:  # 永不过期
            return False
        return time.time() > entry['timestamp'] + entry['timeout']
    
    def _cleanup_expired(self):
        """清理过期的缓存项"""
        expired_keys = [
            key for key, entry in self.cache.items() 
            if self._is_expired(entry)
        ]
        for key in expired_keys:
            del self.cache[key]
    
    def _ensure_capacity(self):
        """确保缓存容量不超限"""
        if len(self.cache) >= self.max_entries:
            # 删除最旧的缓存项
            oldest_key = min(
                self.cache.keys(), 
                key=lambda k: self.cache[k]['timestamp']
            )
            del self.cache[oldest_key]
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        if self._is_expired(entry):
            del self.cache[key]
            return None
        
        # 更新访问时间
        entry['last_access'] = time.time()
        return entry['value']
    
    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> None:
        """设置缓存值"""
        self._cleanup_expired()
        self._ensure_capacity()
        
        if timeout is None:
            timeout = self.default_timeout
        
        self.cache[key] = {
            'value': value,
            'timestamp': time.time(),
            'last_access': time.time(),
            'timeout': timeout
        }
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
    
    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        total_entries = len(self.cache)
        expired_entries = sum(1 for entry in self.cache.values() if self._is_expired(entry))
        
        return {
            'total_entries': total_entries,
            'active_entries': total_entries - expired_entries,
            'expired_entries': expired_entries,
            'max_entries': self.max_entries,
            'memory_usage_mb': sum(
                len(pickle.dumps(entry['value'])) 
                for entry in self.cache.values()
            ) / (1024 * 1024)
        }


class RedisCache:
    """Redis缓存实现"""
    
    def __init__(self, redis_url: str = 'redis://localhost:6379/0', default_timeout: int = 300):
        try:
            import redis
            self.redis_client = redis.from_url(redis_url)
            self.default_timeout = default_timeout
            # 测试连接
            self.redis_client.ping()
            self.available = True
        except Exception as e:
            print(f"Redis连接失败，将使用内存缓存: {e}")
            self.available = False
            self.fallback_cache = MemoryCache()
    
    def _serialize(self, value: Any) -> bytes:
        """序列化值"""
        return pickle.dumps(value)
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化值"""
        return pickle.loads(data)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self.available:
            return self.fallback_cache.get(key)
        
        try:
            data = self.redis_client.get(key)
            if data is None:
                return None
            return self._deserialize(data)
        except Exception as e:
            print(f"Redis获取失败: {e}")
            return None
    
    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> None:
        """设置缓存值"""
        if not self.available:
            return self.fallback_cache.set(key, value, timeout)
        
        if timeout is None:
            timeout = self.default_timeout
        
        try:
            data = self._serialize(value)
            self.redis_client.setex(key, timeout, data)
        except Exception as e:
            print(f"Redis设置失败: {e}")
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        if not self.available:
            return self.fallback_cache.delete(key)
        
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            print(f"Redis删除失败: {e}")
            return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        if not self.available:
            return self.fallback_cache.clear()
        
        try:
            self.redis_client.flushdb()
        except Exception as e:
            print(f"Redis清空失败: {e}")


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        cache_type = CACHE_CONFIG.get('type', 'memory')
        
        if cache_type == 'redis':
            self.cache = RedisCache(
                redis_url=CACHE_CONFIG.get('redis_url', 'redis://localhost:6379/0'),
                default_timeout=CACHE_CONFIG.get('default_timeout', 300)
            )
        else:
            self.cache = MemoryCache(
                max_entries=CACHE_CONFIG.get('max_entries', 1000),
                default_timeout=CACHE_CONFIG.get('default_timeout', 300)
            )
    
    def generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        # 将参数转换为字符串并生成哈希
        key_data = {
            'args': args,
            'kwargs': kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        return self.cache.get(key)
    
    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> None:
        """设置缓存值"""
        self.cache.set(key, value, timeout)
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        return self.cache.delete(key)
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
    
    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        if hasattr(self.cache, 'get_stats'):
            return self.cache.get_stats()
        return {'type': 'redis', 'available': getattr(self.cache, 'available', True)}


def cached(timeout: Optional[int] = None, key_prefix: str = ''):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{cache_manager.generate_key(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


# 全局缓存管理器实例
cache_manager = CacheManager()


def clear_cache():
    """清空所有缓存"""
    cache_manager.clear()


def get_cache_stats() -> Dict:
    """获取缓存统计信息"""
    return cache_manager.get_stats()
