# 升级后的应用依赖包
# 使用最新稳定版本以获得更好的性能和功能

# Dash 框架相关 - 升级到最新版本
dash>=2.17.0
dash-bootstrap-components>=1.6.0

# 数据处理
pandas>=2.1.0
numpy>=1.25.0

# 可视化
plotly>=5.17.0
matplotlib>=3.8.0

# 日期处理
python-dateutil>=2.8.2

# 文件格式支持
openpyxl>=3.1.2  # Excel文件读取
pyarrow>=14.0.0  # Feather文件格式支持

# 性能优化相关
redis>=5.0.0  # 缓存支持（可选）
gunicorn>=21.2.0  # 生产环境WSGI服务器

# 开发工具（可选）
# pytest>=7.4.0  # 测试框架
# black>=23.0.0  # 代码格式化

# 标准库（无需安装）
# json
# os
# itertools
# urllib.parse
