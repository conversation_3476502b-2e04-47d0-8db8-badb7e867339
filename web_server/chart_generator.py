"""
图表生成模块
负责生成各种图表和可视化组件
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from matplotlib import colors
from typing import List, Dict, Optional, Tuple
from config import CHART_CONFIG


class ChartGenerator:
    """图表生成器类"""
    
    def __init__(self):
        self.colors = CHART_CONFIG['colors']
        self.heights = CHART_CONFIG['heights']
        self.layout = CHART_CONFIG['layout']
    
    def hex_or_name_to_rgba(self, color: str, alpha: float = 1.0) -> str:
        """将颜色转换为RGBA格式"""
        try:
            if color.startswith('#'):
                color_rgb = colors.hex2color(color)
                return f'rgba({int(color_rgb[0] * 255)}, {int(color_rgb[1] * 255)}, {int(color_rgb[2] * 255)}, {alpha})'
            elif color in colors.CSS4_COLORS:
                color_rgb = colors.hex2color(colors.CSS4_COLORS[color])
                return f'rgba({int(color_rgb[0] * 255)}, {int(color_rgb[1] * 255)}, {int(color_rgb[2] * 255)}, {alpha})'
            else:
                return f'rgba(255, 0, 0, {alpha})'  # 默认红色
        except Exception:
            return f'rgba(255, 0, 0, {alpha})'  # 默认红色
    
    def create_main_chart(self, data: pd.DataFrame, account_id: str, performance_df: Optional[pd.DataFrame] = None) -> go.Figure:
        """创建主要净值走势图"""
        fig = go.Figure()
        
        # 定义颜色映射
        if account_id == '全部账户汇总':
            color_dict = {
                '策略净值': dict(color=self.colors['strategy']),
                '基准指数': dict(color=self.colors['benchmark']),
                '超额收益': dict(color=self.colors['excess'], dash='dash')
            }
            
            # 检查是否有子账户数据
            all_columns = data.columns.tolist()
            if '股票账户_净值' in all_columns:
                color_dict['股票账户_净值'] = dict(color=self.colors['stock_account'])
            if '期货账户_净值' in all_columns:
                color_dict['期货账户_净值'] = dict(color=self.colors['futures_account'])
        else:
            color_dict = {
                '策略净值': dict(color=self.colors['strategy']),
                '基准指数': dict(color=self.colors['benchmark']),
                '超额收益': dict(color=self.colors['excess'], dash='dash')
            }
        
        # 添加主要曲线
        for name, line_conf in color_dict.items():
            if name in data.columns:
                trace = go.Scatter(
                    x=data['date'],
                    y=data[name],
                    mode='lines',
                    name=name,
                    line=line_conf,
                    hovertemplate=f'{name}: %{{y:.4f}}<br>日期: %{{x}}<extra></extra>'
                )
                fig.add_trace(trace)
        
        # 添加最大回撤区域
        if performance_df is not None:
            self._add_drawdown_areas(fig, data, performance_df)
        
        # 设置布局
        fig.update_layout(
            title='净值走势图',
            xaxis_title='日期',
            yaxis_title='净值',
            hovermode='x unified',
            height=self.heights['main_chart'],
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        return fig
    
    def _add_drawdown_areas(self, fig: go.Figure, data: pd.DataFrame, performance_df: pd.DataFrame):
        """添加最大回撤区域"""
        color_mapping = {
            '策略': (self.colors['strategy'], '策略净值'),
            '基准': (self.colors['benchmark'], '基准指数'),
            '超额': (self.colors['excess'], '超额收益')
        }
        
        for name, (color, column_name) in color_mapping.items():
            if column_name not in data.columns:
                continue
                
            try:
                start_date = str(performance_df.loc[7, name])
                end_date = str(performance_df.loc[8, name])
                
                mask = (data['date'] >= start_date) & (data['date'] <= end_date)
                y_values = data[mask][column_name]
                
                if len(y_values) > 0:
                    fill_trace = go.Scatter(
                        x=data['date'][mask],
                        y=y_values,
                        fill='toself',
                        fillcolor=self.hex_or_name_to_rgba(color, 0.3),
                        line=dict(color=self.hex_or_name_to_rgba(color, 0.8)),
                        name=f'{name}_最大回撤',
                        showlegend=True,
                        hoverinfo='none'
                    )
                    fig.add_trace(fill_trace)
            except Exception as e:
                print(f"Error adding drawdown area for {name}: {e}")
    
    def create_metrics_chart(self, data: pd.DataFrame, selected_metrics: List[str], account_id: str) -> go.Figure:
        """创建指标图表"""
        if not selected_metrics:
            return go.Figure()
        
        num_subplots = len(selected_metrics) + 1
        heights = [self.heights['main_chart']] + [self.heights['sub_chart']] * (len(selected_metrics))
        titles = ['净值走势'] + selected_metrics
        
        fig = make_subplots(
            rows=num_subplots,
            cols=1,
            shared_xaxes=True,
            subplot_titles=tuple(titles),
            row_heights=heights,
            vertical_spacing=self.layout['vertical_spacing']
        )
        
        # 添加主图（净值走势）
        main_fig = self.create_main_chart(data, account_id)
        for trace in main_fig.data:
            fig.add_trace(trace, row=1, col=1)
        
        # 添加指标子图
        showlegend = (account_id == '全部账户汇总')
        all_columns = data.columns.tolist()
        
        for i, metric in enumerate(selected_metrics, start=2):
            metric_columns = [col for col in all_columns if metric in col]
            
            for j, col in enumerate(metric_columns):
                color = self.colors['metrics'][j % len(self.colors['metrics'])]
                
                if metric == '成交金额':
                    # 柱状图
                    trace = go.Bar(
                        x=data['date'],
                        y=data[col],
                        name=col,
                        showlegend=showlegend,
                        marker_color=color,
                        hovertemplate=f'{col}: %{{y:.2f}}<br>日期: %{{x}}<extra></extra>'
                    )
                else:
                    # 线图
                    trace = go.Scatter(
                        x=data['date'],
                        y=data[col],
                        mode='lines',
                        name=col,
                        showlegend=showlegend,
                        line=dict(color=color),
                        hovertemplate=f'{col}: %{{y:.2f}}<br>日期: %{{x}}<extra></extra>'
                    )
                
                fig.add_trace(trace, row=i, col=1)
            
            # 更新子图x轴
            fig.update_xaxes(
                row=i, col=1,
                showticklabels=True,
                tickformat='%Y-%m-%d',
                ticklabelmode="period"
            )
        
        # 设置整体布局
        total_height = sum(heights)
        fig.update_layout(
            height=total_height,
            hovermode='x unified',
            bargap=0,
            showlegend=True
        )
        
        # 设置x轴刻度
        self._set_x_axis_ticks(fig, data, num_subplots)
        
        return fig
    
    def _set_x_axis_ticks(self, fig: go.Figure, data: pd.DataFrame, num_subplots: int):
        """设置x轴刻度"""
        num_ticks = self.layout['num_ticks']
        
        if len(data) <= num_ticks:
            tick_values = list(range(len(data)))
        else:
            tick_values = [0, len(data) - 1]
            if num_ticks > 2:
                middle_ticks = np.linspace(0, len(data) - 1, num_ticks - 2).astype(int)[1:-1]
                tick_values = np.concatenate(([0], middle_ticks, [len(data) - 1]))
        
        for i in range(1, num_subplots + 1):
            fig.update_xaxes(
                row=i, col=1,
                type='category',
                tickvals=tick_values
            )
    
    def create_simple_balance_chart(self, data: pd.DataFrame, account_id: str) -> go.Figure:
        """创建简单的资产走势图"""
        fig = go.Figure()
        
        trace = go.Scatter(
            x=data['date'],
            y=data['balance'],
            mode='lines',
            name=f'账户：{account_id} 资产走势图',
            line=dict(color=self.colors['strategy']),
            hovertemplate='资产: %{y:.2f}<br>日期: %{x}<extra></extra>'
        )
        
        fig.add_trace(trace)
        
        fig.update_layout(
            title=f'账户 {account_id} 资产走势',
            xaxis_title='日期',
            yaxis_title='资产',
            height=self.heights['main_chart'],
            hovermode='x unified'
        )
        
        return fig


# 全局图表生成器实例
chart_generator = ChartGenerator()
