// This file is generated by Tools/cases_generator/opcode_metadata_generator.py
// from:
//   Python/bytecodes.c
// Do not edit!

#ifndef Py_CORE_OPCODE_METADATA_H
#define Py_CORE_OPCODE_METADATA_H
#ifdef __cplusplus
extern "C" {
#endif

#ifndef Py_BUILD_CORE
#  error "this header requires Py_BUILD_CORE define"
#endif

#include <stdbool.h>              // bool
#include "opcode_ids.h"


#define IS_PSEUDO_INSTR(OP)  ( \
    ((OP) == LOAD_CLOSURE) || \
    ((OP) == STORE_FAST_MAYBE_NULL) || \
    ((OP) == LOAD_SUPER_METHOD) || \
    ((OP) == LOAD_ZERO_SUPER_METHOD) || \
    ((OP) == LOAD_ZERO_SUPER_ATTR) || \
    ((OP) == LOAD_METHOD) || \
    ((OP) == JUMP) || \
    ((OP) == JUMP_NO_INTERRUPT) || \
    ((OP) == SETUP_FINALLY) || \
    ((OP) == SETUP_CLEANUP) || \
    ((OP) == SETUP_WITH) || \
    ((OP) == POP_BLOCK) || \
    0)

#include "pycore_uop_ids.h"
extern int _PyOpcode_num_popped(int opcode, int oparg);
#ifdef NEED_OPCODE_METADATA
int _PyOpcode_num_popped(int opcode, int oparg)  {
    switch(opcode) {
        case BEFORE_ASYNC_WITH:
            return 1;
        case BEFORE_WITH:
            return 1;
        case BINARY_OP:
            return 2;
        case BINARY_OP_ADD_FLOAT:
            return 2;
        case BINARY_OP_ADD_INT:
            return 2;
        case BINARY_OP_ADD_UNICODE:
            return 2;
        case BINARY_OP_INPLACE_ADD_UNICODE:
            return 2;
        case BINARY_OP_MULTIPLY_FLOAT:
            return 2;
        case BINARY_OP_MULTIPLY_INT:
            return 2;
        case BINARY_OP_SUBTRACT_FLOAT:
            return 2;
        case BINARY_OP_SUBTRACT_INT:
            return 2;
        case BINARY_SLICE:
            return 3;
        case BINARY_SUBSCR:
            return 2;
        case BINARY_SUBSCR_DICT:
            return 2;
        case BINARY_SUBSCR_GETITEM:
            return 2;
        case BINARY_SUBSCR_LIST_INT:
            return 2;
        case BINARY_SUBSCR_STR_INT:
            return 2;
        case BINARY_SUBSCR_TUPLE_INT:
            return 2;
        case BUILD_CONST_KEY_MAP:
            return 1 + oparg;
        case BUILD_LIST:
            return oparg;
        case BUILD_MAP:
            return oparg*2;
        case BUILD_SET:
            return oparg;
        case BUILD_SLICE:
            return 2 + ((oparg == 3) ? 1 : 0);
        case BUILD_STRING:
            return oparg;
        case BUILD_TUPLE:
            return oparg;
        case CACHE:
            return 0;
        case CALL:
            return 2 + oparg;
        case CALL_ALLOC_AND_ENTER_INIT:
            return 2 + oparg;
        case CALL_BOUND_METHOD_EXACT_ARGS:
            return 2 + oparg;
        case CALL_BOUND_METHOD_GENERAL:
            return 2 + oparg;
        case CALL_BUILTIN_CLASS:
            return 2 + oparg;
        case CALL_BUILTIN_FAST:
            return 2 + oparg;
        case CALL_BUILTIN_FAST_WITH_KEYWORDS:
            return 2 + oparg;
        case CALL_BUILTIN_O:
            return 2 + oparg;
        case CALL_FUNCTION_EX:
            return 3 + (oparg & 1);
        case CALL_INTRINSIC_1:
            return 1;
        case CALL_INTRINSIC_2:
            return 2;
        case CALL_ISINSTANCE:
            return 2 + oparg;
        case CALL_KW:
            return 3 + oparg;
        case CALL_LEN:
            return 2 + oparg;
        case CALL_LIST_APPEND:
            return 3;
        case CALL_METHOD_DESCRIPTOR_FAST:
            return 2 + oparg;
        case CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS:
            return 2 + oparg;
        case CALL_METHOD_DESCRIPTOR_NOARGS:
            return 2 + oparg;
        case CALL_METHOD_DESCRIPTOR_O:
            return 2 + oparg;
        case CALL_NON_PY_GENERAL:
            return 2 + oparg;
        case CALL_PY_EXACT_ARGS:
            return 2 + oparg;
        case CALL_PY_GENERAL:
            return 2 + oparg;
        case CALL_STR_1:
            return 3;
        case CALL_TUPLE_1:
            return 3;
        case CALL_TYPE_1:
            return 3;
        case CHECK_EG_MATCH:
            return 2;
        case CHECK_EXC_MATCH:
            return 2;
        case CLEANUP_THROW:
            return 3;
        case COMPARE_OP:
            return 2;
        case COMPARE_OP_FLOAT:
            return 2;
        case COMPARE_OP_INT:
            return 2;
        case COMPARE_OP_STR:
            return 2;
        case CONTAINS_OP:
            return 2;
        case CONTAINS_OP_DICT:
            return 2;
        case CONTAINS_OP_SET:
            return 2;
        case CONVERT_VALUE:
            return 1;
        case COPY:
            return 1 + (oparg-1);
        case COPY_FREE_VARS:
            return 0;
        case DELETE_ATTR:
            return 1;
        case DELETE_DEREF:
            return 0;
        case DELETE_FAST:
            return 0;
        case DELETE_GLOBAL:
            return 0;
        case DELETE_NAME:
            return 0;
        case DELETE_SUBSCR:
            return 2;
        case DICT_MERGE:
            return 5 + (oparg - 1);
        case DICT_UPDATE:
            return 2 + (oparg - 1);
        case END_ASYNC_FOR:
            return 2;
        case END_FOR:
            return 1;
        case END_SEND:
            return 2;
        case ENTER_EXECUTOR:
            return 0;
        case EXIT_INIT_CHECK:
            return 1;
        case EXTENDED_ARG:
            return 0;
        case FORMAT_SIMPLE:
            return 1;
        case FORMAT_WITH_SPEC:
            return 2;
        case FOR_ITER:
            return 1;
        case FOR_ITER_GEN:
            return 1;
        case FOR_ITER_LIST:
            return 1;
        case FOR_ITER_RANGE:
            return 1;
        case FOR_ITER_TUPLE:
            return 1;
        case GET_AITER:
            return 1;
        case GET_ANEXT:
            return 1;
        case GET_AWAITABLE:
            return 1;
        case GET_ITER:
            return 1;
        case GET_LEN:
            return 1;
        case GET_YIELD_FROM_ITER:
            return 1;
        case IMPORT_FROM:
            return 1;
        case IMPORT_NAME:
            return 2;
        case INSTRUMENTED_CALL:
            return 0;
        case INSTRUMENTED_CALL_FUNCTION_EX:
            return 0;
        case INSTRUMENTED_CALL_KW:
            return 0;
        case INSTRUMENTED_END_FOR:
            return 2;
        case INSTRUMENTED_END_SEND:
            return 2;
        case INSTRUMENTED_FOR_ITER:
            return 0;
        case INSTRUMENTED_INSTRUCTION:
            return 0;
        case INSTRUMENTED_JUMP_BACKWARD:
            return 0;
        case INSTRUMENTED_JUMP_FORWARD:
            return 0;
        case INSTRUMENTED_LOAD_SUPER_ATTR:
            return 3;
        case INSTRUMENTED_POP_JUMP_IF_FALSE:
            return 0;
        case INSTRUMENTED_POP_JUMP_IF_NONE:
            return 0;
        case INSTRUMENTED_POP_JUMP_IF_NOT_NONE:
            return 0;
        case INSTRUMENTED_POP_JUMP_IF_TRUE:
            return 0;
        case INSTRUMENTED_RESUME:
            return 0;
        case INSTRUMENTED_RETURN_CONST:
            return 0;
        case INSTRUMENTED_RETURN_VALUE:
            return 1;
        case INSTRUMENTED_YIELD_VALUE:
            return 1;
        case INTERPRETER_EXIT:
            return 1;
        case IS_OP:
            return 2;
        case JUMP_BACKWARD:
            return 0;
        case JUMP_BACKWARD_NO_INTERRUPT:
            return 0;
        case JUMP_FORWARD:
            return 0;
        case LIST_APPEND:
            return 2 + (oparg-1);
        case LIST_EXTEND:
            return 2 + (oparg-1);
        case LOAD_ASSERTION_ERROR:
            return 0;
        case LOAD_ATTR:
            return 1;
        case LOAD_ATTR_CLASS:
            return 1;
        case LOAD_ATTR_GETATTRIBUTE_OVERRIDDEN:
            return 1;
        case LOAD_ATTR_INSTANCE_VALUE:
            return 1;
        case LOAD_ATTR_METHOD_LAZY_DICT:
            return 1;
        case LOAD_ATTR_METHOD_NO_DICT:
            return 1;
        case LOAD_ATTR_METHOD_WITH_VALUES:
            return 1;
        case LOAD_ATTR_MODULE:
            return 1;
        case LOAD_ATTR_NONDESCRIPTOR_NO_DICT:
            return 1;
        case LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES:
            return 1;
        case LOAD_ATTR_PROPERTY:
            return 1;
        case LOAD_ATTR_SLOT:
            return 1;
        case LOAD_ATTR_WITH_HINT:
            return 1;
        case LOAD_BUILD_CLASS:
            return 0;
        case LOAD_CONST:
            return 0;
        case LOAD_DEREF:
            return 0;
        case LOAD_FAST:
            return 0;
        case LOAD_FAST_AND_CLEAR:
            return 0;
        case LOAD_FAST_CHECK:
            return 0;
        case LOAD_FAST_LOAD_FAST:
            return 0;
        case LOAD_FROM_DICT_OR_DEREF:
            return 1;
        case LOAD_FROM_DICT_OR_GLOBALS:
            return 1;
        case LOAD_GLOBAL:
            return 0;
        case LOAD_GLOBAL_BUILTIN:
            return 0;
        case LOAD_GLOBAL_MODULE:
            return 0;
        case LOAD_LOCALS:
            return 0;
        case LOAD_NAME:
            return 0;
        case LOAD_SUPER_ATTR:
            return 3;
        case LOAD_SUPER_ATTR_ATTR:
            return 3;
        case LOAD_SUPER_ATTR_METHOD:
            return 3;
        case MAKE_CELL:
            return 0;
        case MAKE_FUNCTION:
            return 1;
        case MAP_ADD:
            return 3 + (oparg - 1);
        case MATCH_CLASS:
            return 3;
        case MATCH_KEYS:
            return 2;
        case MATCH_MAPPING:
            return 1;
        case MATCH_SEQUENCE:
            return 1;
        case NOP:
            return 0;
        case POP_EXCEPT:
            return 1;
        case POP_JUMP_IF_FALSE:
            return 1;
        case POP_JUMP_IF_NONE:
            return 1;
        case POP_JUMP_IF_NOT_NONE:
            return 1;
        case POP_JUMP_IF_TRUE:
            return 1;
        case POP_TOP:
            return 1;
        case PUSH_EXC_INFO:
            return 1;
        case PUSH_NULL:
            return 0;
        case RAISE_VARARGS:
            return oparg;
        case RERAISE:
            return 1 + oparg;
        case RESERVED:
            return 0;
        case RESUME:
            return 0;
        case RESUME_CHECK:
            return 0;
        case RETURN_CONST:
            return 0;
        case RETURN_GENERATOR:
            return 0;
        case RETURN_VALUE:
            return 1;
        case SEND:
            return 2;
        case SEND_GEN:
            return 2;
        case SETUP_ANNOTATIONS:
            return 0;
        case SET_ADD:
            return 2 + (oparg-1);
        case SET_FUNCTION_ATTRIBUTE:
            return 2;
        case SET_UPDATE:
            return 2 + (oparg-1);
        case STORE_ATTR:
            return 2;
        case STORE_ATTR_INSTANCE_VALUE:
            return 2;
        case STORE_ATTR_SLOT:
            return 2;
        case STORE_ATTR_WITH_HINT:
            return 2;
        case STORE_DEREF:
            return 1;
        case STORE_FAST:
            return 1;
        case STORE_FAST_LOAD_FAST:
            return 1;
        case STORE_FAST_STORE_FAST:
            return 2;
        case STORE_GLOBAL:
            return 1;
        case STORE_NAME:
            return 1;
        case STORE_SLICE:
            return 4;
        case STORE_SUBSCR:
            return 3;
        case STORE_SUBSCR_DICT:
            return 3;
        case STORE_SUBSCR_LIST_INT:
            return 3;
        case SWAP:
            return 2 + (oparg-2);
        case TO_BOOL:
            return 1;
        case TO_BOOL_ALWAYS_TRUE:
            return 1;
        case TO_BOOL_BOOL:
            return 1;
        case TO_BOOL_INT:
            return 1;
        case TO_BOOL_LIST:
            return 1;
        case TO_BOOL_NONE:
            return 1;
        case TO_BOOL_STR:
            return 1;
        case UNARY_INVERT:
            return 1;
        case UNARY_NEGATIVE:
            return 1;
        case UNARY_NOT:
            return 1;
        case UNPACK_EX:
            return 1;
        case UNPACK_SEQUENCE:
            return 1;
        case UNPACK_SEQUENCE_LIST:
            return 1;
        case UNPACK_SEQUENCE_TUPLE:
            return 1;
        case UNPACK_SEQUENCE_TWO_TUPLE:
            return 1;
        case WITH_EXCEPT_START:
            return 4;
        case YIELD_VALUE:
            return 1;
        default:
            return -1;
    }
}

#endif

extern int _PyOpcode_num_pushed(int opcode, int oparg);
#ifdef NEED_OPCODE_METADATA
int _PyOpcode_num_pushed(int opcode, int oparg)  {
    switch(opcode) {
        case BEFORE_ASYNC_WITH:
            return 2;
        case BEFORE_WITH:
            return 2;
        case BINARY_OP:
            return 1;
        case BINARY_OP_ADD_FLOAT:
            return 1;
        case BINARY_OP_ADD_INT:
            return 1;
        case BINARY_OP_ADD_UNICODE:
            return 1;
        case BINARY_OP_INPLACE_ADD_UNICODE:
            return 0;
        case BINARY_OP_MULTIPLY_FLOAT:
            return 1;
        case BINARY_OP_MULTIPLY_INT:
            return 1;
        case BINARY_OP_SUBTRACT_FLOAT:
            return 1;
        case BINARY_OP_SUBTRACT_INT:
            return 1;
        case BINARY_SLICE:
            return 1;
        case BINARY_SUBSCR:
            return 1;
        case BINARY_SUBSCR_DICT:
            return 1;
        case BINARY_SUBSCR_GETITEM:
            return 1;
        case BINARY_SUBSCR_LIST_INT:
            return 1;
        case BINARY_SUBSCR_STR_INT:
            return 1;
        case BINARY_SUBSCR_TUPLE_INT:
            return 1;
        case BUILD_CONST_KEY_MAP:
            return 1;
        case BUILD_LIST:
            return 1;
        case BUILD_MAP:
            return 1;
        case BUILD_SET:
            return 1;
        case BUILD_SLICE:
            return 1;
        case BUILD_STRING:
            return 1;
        case BUILD_TUPLE:
            return 1;
        case CACHE:
            return 0;
        case CALL:
            return 1;
        case CALL_ALLOC_AND_ENTER_INIT:
            return 1;
        case CALL_BOUND_METHOD_EXACT_ARGS:
            return 0;
        case CALL_BOUND_METHOD_GENERAL:
            return 0;
        case CALL_BUILTIN_CLASS:
            return 1;
        case CALL_BUILTIN_FAST:
            return 1;
        case CALL_BUILTIN_FAST_WITH_KEYWORDS:
            return 1;
        case CALL_BUILTIN_O:
            return 1;
        case CALL_FUNCTION_EX:
            return 1;
        case CALL_INTRINSIC_1:
            return 1;
        case CALL_INTRINSIC_2:
            return 1;
        case CALL_ISINSTANCE:
            return 1;
        case CALL_KW:
            return 1;
        case CALL_LEN:
            return 1;
        case CALL_LIST_APPEND:
            return 1;
        case CALL_METHOD_DESCRIPTOR_FAST:
            return 1;
        case CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS:
            return 1;
        case CALL_METHOD_DESCRIPTOR_NOARGS:
            return 1;
        case CALL_METHOD_DESCRIPTOR_O:
            return 1;
        case CALL_NON_PY_GENERAL:
            return 1;
        case CALL_PY_EXACT_ARGS:
            return 0;
        case CALL_PY_GENERAL:
            return 0;
        case CALL_STR_1:
            return 1;
        case CALL_TUPLE_1:
            return 1;
        case CALL_TYPE_1:
            return 1;
        case CHECK_EG_MATCH:
            return 2;
        case CHECK_EXC_MATCH:
            return 2;
        case CLEANUP_THROW:
            return 2;
        case COMPARE_OP:
            return 1;
        case COMPARE_OP_FLOAT:
            return 1;
        case COMPARE_OP_INT:
            return 1;
        case COMPARE_OP_STR:
            return 1;
        case CONTAINS_OP:
            return 1;
        case CONTAINS_OP_DICT:
            return 1;
        case CONTAINS_OP_SET:
            return 1;
        case CONVERT_VALUE:
            return 1;
        case COPY:
            return 2 + (oparg-1);
        case COPY_FREE_VARS:
            return 0;
        case DELETE_ATTR:
            return 0;
        case DELETE_DEREF:
            return 0;
        case DELETE_FAST:
            return 0;
        case DELETE_GLOBAL:
            return 0;
        case DELETE_NAME:
            return 0;
        case DELETE_SUBSCR:
            return 0;
        case DICT_MERGE:
            return 4 + (oparg - 1);
        case DICT_UPDATE:
            return 1 + (oparg - 1);
        case END_ASYNC_FOR:
            return 0;
        case END_FOR:
            return 0;
        case END_SEND:
            return 1;
        case ENTER_EXECUTOR:
            return 0;
        case EXIT_INIT_CHECK:
            return 0;
        case EXTENDED_ARG:
            return 0;
        case FORMAT_SIMPLE:
            return 1;
        case FORMAT_WITH_SPEC:
            return 1;
        case FOR_ITER:
            return 2;
        case FOR_ITER_GEN:
            return 1;
        case FOR_ITER_LIST:
            return 2;
        case FOR_ITER_RANGE:
            return 2;
        case FOR_ITER_TUPLE:
            return 2;
        case GET_AITER:
            return 1;
        case GET_ANEXT:
            return 2;
        case GET_AWAITABLE:
            return 1;
        case GET_ITER:
            return 1;
        case GET_LEN:
            return 2;
        case GET_YIELD_FROM_ITER:
            return 1;
        case IMPORT_FROM:
            return 2;
        case IMPORT_NAME:
            return 1;
        case INSTRUMENTED_CALL:
            return 0;
        case INSTRUMENTED_CALL_FUNCTION_EX:
            return 0;
        case INSTRUMENTED_CALL_KW:
            return 0;
        case INSTRUMENTED_END_FOR:
            return 1;
        case INSTRUMENTED_END_SEND:
            return 1;
        case INSTRUMENTED_FOR_ITER:
            return 0;
        case INSTRUMENTED_INSTRUCTION:
            return 0;
        case INSTRUMENTED_JUMP_BACKWARD:
            return 0;
        case INSTRUMENTED_JUMP_FORWARD:
            return 0;
        case INSTRUMENTED_LOAD_SUPER_ATTR:
            return 1 + (oparg & 1);
        case INSTRUMENTED_POP_JUMP_IF_FALSE:
            return 0;
        case INSTRUMENTED_POP_JUMP_IF_NONE:
            return 0;
        case INSTRUMENTED_POP_JUMP_IF_NOT_NONE:
            return 0;
        case INSTRUMENTED_POP_JUMP_IF_TRUE:
            return 0;
        case INSTRUMENTED_RESUME:
            return 0;
        case INSTRUMENTED_RETURN_CONST:
            return 0;
        case INSTRUMENTED_RETURN_VALUE:
            return 0;
        case INSTRUMENTED_YIELD_VALUE:
            return 1;
        case INTERPRETER_EXIT:
            return 0;
        case IS_OP:
            return 1;
        case JUMP_BACKWARD:
            return 0;
        case JUMP_BACKWARD_NO_INTERRUPT:
            return 0;
        case JUMP_FORWARD:
            return 0;
        case LIST_APPEND:
            return 1 + (oparg-1);
        case LIST_EXTEND:
            return 1 + (oparg-1);
        case LOAD_ASSERTION_ERROR:
            return 1;
        case LOAD_ATTR:
            return 1 + (oparg & 1);
        case LOAD_ATTR_CLASS:
            return 1 + (oparg & 1);
        case LOAD_ATTR_GETATTRIBUTE_OVERRIDDEN:
            return 1;
        case LOAD_ATTR_INSTANCE_VALUE:
            return 1 + (oparg & 1);
        case LOAD_ATTR_METHOD_LAZY_DICT:
            return 2;
        case LOAD_ATTR_METHOD_NO_DICT:
            return 2;
        case LOAD_ATTR_METHOD_WITH_VALUES:
            return 2;
        case LOAD_ATTR_MODULE:
            return 1 + (oparg & 1);
        case LOAD_ATTR_NONDESCRIPTOR_NO_DICT:
            return 1;
        case LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES:
            return 1;
        case LOAD_ATTR_PROPERTY:
            return 1;
        case LOAD_ATTR_SLOT:
            return 1 + (oparg & 1);
        case LOAD_ATTR_WITH_HINT:
            return 1 + (oparg & 1);
        case LOAD_BUILD_CLASS:
            return 1;
        case LOAD_CONST:
            return 1;
        case LOAD_DEREF:
            return 1;
        case LOAD_FAST:
            return 1;
        case LOAD_FAST_AND_CLEAR:
            return 1;
        case LOAD_FAST_CHECK:
            return 1;
        case LOAD_FAST_LOAD_FAST:
            return 2;
        case LOAD_FROM_DICT_OR_DEREF:
            return 1;
        case LOAD_FROM_DICT_OR_GLOBALS:
            return 1;
        case LOAD_GLOBAL:
            return 1 + (oparg & 1);
        case LOAD_GLOBAL_BUILTIN:
            return 1 + (oparg & 1);
        case LOAD_GLOBAL_MODULE:
            return 1 + (oparg & 1);
        case LOAD_LOCALS:
            return 1;
        case LOAD_NAME:
            return 1;
        case LOAD_SUPER_ATTR:
            return 1 + (oparg & 1);
        case LOAD_SUPER_ATTR_ATTR:
            return 1;
        case LOAD_SUPER_ATTR_METHOD:
            return 2;
        case MAKE_CELL:
            return 0;
        case MAKE_FUNCTION:
            return 1;
        case MAP_ADD:
            return 1 + (oparg - 1);
        case MATCH_CLASS:
            return 1;
        case MATCH_KEYS:
            return 3;
        case MATCH_MAPPING:
            return 2;
        case MATCH_SEQUENCE:
            return 2;
        case NOP:
            return 0;
        case POP_EXCEPT:
            return 0;
        case POP_JUMP_IF_FALSE:
            return 0;
        case POP_JUMP_IF_NONE:
            return 0;
        case POP_JUMP_IF_NOT_NONE:
            return 0;
        case POP_JUMP_IF_TRUE:
            return 0;
        case POP_TOP:
            return 0;
        case PUSH_EXC_INFO:
            return 2;
        case PUSH_NULL:
            return 1;
        case RAISE_VARARGS:
            return 0;
        case RERAISE:
            return oparg;
        case RESERVED:
            return 0;
        case RESUME:
            return 0;
        case RESUME_CHECK:
            return 0;
        case RETURN_CONST:
            return 0;
        case RETURN_GENERATOR:
            return 1;
        case RETURN_VALUE:
            return 0;
        case SEND:
            return 2;
        case SEND_GEN:
            return 2;
        case SETUP_ANNOTATIONS:
            return 0;
        case SET_ADD:
            return 1 + (oparg-1);
        case SET_FUNCTION_ATTRIBUTE:
            return 1;
        case SET_UPDATE:
            return 1 + (oparg-1);
        case STORE_ATTR:
            return 0;
        case STORE_ATTR_INSTANCE_VALUE:
            return 0;
        case STORE_ATTR_SLOT:
            return 0;
        case STORE_ATTR_WITH_HINT:
            return 0;
        case STORE_DEREF:
            return 0;
        case STORE_FAST:
            return 0;
        case STORE_FAST_LOAD_FAST:
            return 1;
        case STORE_FAST_STORE_FAST:
            return 0;
        case STORE_GLOBAL:
            return 0;
        case STORE_NAME:
            return 0;
        case STORE_SLICE:
            return 0;
        case STORE_SUBSCR:
            return 0;
        case STORE_SUBSCR_DICT:
            return 0;
        case STORE_SUBSCR_LIST_INT:
            return 0;
        case SWAP:
            return 2 + (oparg-2);
        case TO_BOOL:
            return 1;
        case TO_BOOL_ALWAYS_TRUE:
            return 1;
        case TO_BOOL_BOOL:
            return 1;
        case TO_BOOL_INT:
            return 1;
        case TO_BOOL_LIST:
            return 1;
        case TO_BOOL_NONE:
            return 1;
        case TO_BOOL_STR:
            return 1;
        case UNARY_INVERT:
            return 1;
        case UNARY_NEGATIVE:
            return 1;
        case UNARY_NOT:
            return 1;
        case UNPACK_EX:
            return 1 + (oparg >> 8) + (oparg & 0xFF);
        case UNPACK_SEQUENCE:
            return oparg;
        case UNPACK_SEQUENCE_LIST:
            return oparg;
        case UNPACK_SEQUENCE_TUPLE:
            return oparg;
        case UNPACK_SEQUENCE_TWO_TUPLE:
            return 2;
        case WITH_EXCEPT_START:
            return 5;
        case YIELD_VALUE:
            return 1;
        default:
            return -1;
    }
}

#endif

enum InstructionFormat {
    INSTR_FMT_IB = 1,
    INSTR_FMT_IBC = 2,
    INSTR_FMT_IBC00 = 3,
    INSTR_FMT_IBC000 = 4,
    INSTR_FMT_IBC00000000 = 5,
    INSTR_FMT_IX = 6,
    INSTR_FMT_IXC = 7,
    INSTR_FMT_IXC00 = 8,
    INSTR_FMT_IXC000 = 9,
};

#define IS_VALID_OPCODE(OP) \
    (((OP) >= 0) && ((OP) < 268) && \
     (_PyOpcode_opcode_metadata[(OP)].valid_entry))

#define HAS_ARG_FLAG (1)
#define HAS_CONST_FLAG (2)
#define HAS_NAME_FLAG (4)
#define HAS_JUMP_FLAG (8)
#define HAS_FREE_FLAG (16)
#define HAS_LOCAL_FLAG (32)
#define HAS_EVAL_BREAK_FLAG (64)
#define HAS_DEOPT_FLAG (128)
#define HAS_ERROR_FLAG (256)
#define HAS_ESCAPES_FLAG (512)
#define HAS_EXIT_FLAG (1024)
#define HAS_PURE_FLAG (2048)
#define HAS_PASSTHROUGH_FLAG (4096)
#define HAS_OPARG_AND_1_FLAG (8192)
#define HAS_ERROR_NO_POP_FLAG (16384)
#define OPCODE_HAS_ARG(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_ARG_FLAG))
#define OPCODE_HAS_CONST(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_CONST_FLAG))
#define OPCODE_HAS_NAME(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_NAME_FLAG))
#define OPCODE_HAS_JUMP(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_JUMP_FLAG))
#define OPCODE_HAS_FREE(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_FREE_FLAG))
#define OPCODE_HAS_LOCAL(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_LOCAL_FLAG))
#define OPCODE_HAS_EVAL_BREAK(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_EVAL_BREAK_FLAG))
#define OPCODE_HAS_DEOPT(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_DEOPT_FLAG))
#define OPCODE_HAS_ERROR(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_ERROR_FLAG))
#define OPCODE_HAS_ESCAPES(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_ESCAPES_FLAG))
#define OPCODE_HAS_EXIT(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_EXIT_FLAG))
#define OPCODE_HAS_PURE(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_PURE_FLAG))
#define OPCODE_HAS_PASSTHROUGH(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_PASSTHROUGH_FLAG))
#define OPCODE_HAS_OPARG_AND_1(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_OPARG_AND_1_FLAG))
#define OPCODE_HAS_ERROR_NO_POP(OP) (_PyOpcode_opcode_metadata[OP].flags & (HAS_ERROR_NO_POP_FLAG))

#define OPARG_FULL 0
#define OPARG_CACHE_1 1
#define OPARG_CACHE_2 2
#define OPARG_CACHE_4 4
#define OPARG_TOP 5
#define OPARG_BOTTOM 6
#define OPARG_SAVE_RETURN_OFFSET 7
#define OPARG_REPLACED 9

struct opcode_metadata {
    uint8_t valid_entry;
    int8_t instr_format;
    int16_t flags;
};

extern const struct opcode_metadata _PyOpcode_opcode_metadata[268];
#ifdef NEED_OPCODE_METADATA
const struct opcode_metadata _PyOpcode_opcode_metadata[268] = {
    [BEFORE_ASYNC_WITH] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [BEFORE_WITH] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [BINARY_OP] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [BINARY_OP_ADD_FLOAT] = { true, INSTR_FMT_IXC, HAS_EXIT_FLAG },
    [BINARY_OP_ADD_INT] = { true, INSTR_FMT_IXC, HAS_EXIT_FLAG | HAS_ERROR_FLAG },
    [BINARY_OP_ADD_UNICODE] = { true, INSTR_FMT_IXC, HAS_EXIT_FLAG | HAS_ERROR_FLAG },
    [BINARY_OP_INPLACE_ADD_UNICODE] = { true, INSTR_FMT_IXC, HAS_LOCAL_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [BINARY_OP_MULTIPLY_FLOAT] = { true, INSTR_FMT_IXC, HAS_EXIT_FLAG },
    [BINARY_OP_MULTIPLY_INT] = { true, INSTR_FMT_IXC, HAS_EXIT_FLAG | HAS_ERROR_FLAG },
    [BINARY_OP_SUBTRACT_FLOAT] = { true, INSTR_FMT_IXC, HAS_EXIT_FLAG },
    [BINARY_OP_SUBTRACT_INT] = { true, INSTR_FMT_IXC, HAS_EXIT_FLAG | HAS_ERROR_FLAG },
    [BINARY_SLICE] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [BINARY_SUBSCR] = { true, INSTR_FMT_IXC, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [BINARY_SUBSCR_DICT] = { true, INSTR_FMT_IXC, HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [BINARY_SUBSCR_GETITEM] = { true, INSTR_FMT_IXC, HAS_DEOPT_FLAG | HAS_ESCAPES_FLAG },
    [BINARY_SUBSCR_LIST_INT] = { true, INSTR_FMT_IXC, HAS_DEOPT_FLAG },
    [BINARY_SUBSCR_STR_INT] = { true, INSTR_FMT_IXC, HAS_DEOPT_FLAG },
    [BINARY_SUBSCR_TUPLE_INT] = { true, INSTR_FMT_IXC, HAS_DEOPT_FLAG },
    [BUILD_CONST_KEY_MAP] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [BUILD_LIST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG },
    [BUILD_MAP] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [BUILD_SET] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [BUILD_SLICE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG },
    [BUILD_STRING] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG },
    [BUILD_TUPLE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG },
    [CACHE] = { true, INSTR_FMT_IX, 0 },
    [CALL] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_ALLOC_AND_ENTER_INIT] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_BOUND_METHOD_EXACT_ARGS] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [CALL_BOUND_METHOD_GENERAL] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_BUILTIN_CLASS] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_BUILTIN_FAST] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_BUILTIN_FAST_WITH_KEYWORDS] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_BUILTIN_O] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_FUNCTION_EX] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_INTRINSIC_1] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_INTRINSIC_2] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_ISINSTANCE] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_KW] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_LEN] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_LIST_APPEND] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG },
    [CALL_METHOD_DESCRIPTOR_FAST] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_METHOD_DESCRIPTOR_NOARGS] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_METHOD_DESCRIPTOR_O] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_NON_PY_GENERAL] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_EXIT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_PY_EXACT_ARGS] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [CALL_PY_GENERAL] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [CALL_STR_1] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_TUPLE_1] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CALL_TYPE_1] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [CHECK_EG_MATCH] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CHECK_EXC_MATCH] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CLEANUP_THROW] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [COMPARE_OP] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [COMPARE_OP_FLOAT] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_EXIT_FLAG },
    [COMPARE_OP_INT] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [COMPARE_OP_STR] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_EXIT_FLAG },
    [CONTAINS_OP] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CONTAINS_OP_DICT] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CONTAINS_OP_SET] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [CONVERT_VALUE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG },
    [COPY] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_PURE_FLAG },
    [COPY_FREE_VARS] = { true, INSTR_FMT_IB, HAS_ARG_FLAG },
    [DELETE_ATTR] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [DELETE_DEREF] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_FREE_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [DELETE_FAST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [DELETE_GLOBAL] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [DELETE_NAME] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [DELETE_SUBSCR] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [DICT_MERGE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [DICT_UPDATE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [END_ASYNC_FOR] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [END_FOR] = { true, INSTR_FMT_IX, HAS_PURE_FLAG },
    [END_SEND] = { true, INSTR_FMT_IX, HAS_PURE_FLAG },
    [ENTER_EXECUTOR] = { true, INSTR_FMT_IB, HAS_ARG_FLAG },
    [EXIT_INIT_CHECK] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [EXTENDED_ARG] = { true, INSTR_FMT_IB, HAS_ARG_FLAG },
    [FORMAT_SIMPLE] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [FORMAT_WITH_SPEC] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [FOR_ITER] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [FOR_ITER_GEN] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [FOR_ITER_LIST] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG | HAS_EXIT_FLAG },
    [FOR_ITER_RANGE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG | HAS_EXIT_FLAG | HAS_ERROR_FLAG },
    [FOR_ITER_TUPLE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG | HAS_EXIT_FLAG },
    [GET_AITER] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [GET_ANEXT] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [GET_AWAITABLE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [GET_ITER] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [GET_LEN] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [GET_YIELD_FROM_ITER] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [IMPORT_FROM] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [IMPORT_NAME] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_CALL] = { true, INSTR_FMT_IBC00, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_CALL_FUNCTION_EX] = { true, INSTR_FMT_IX, 0 },
    [INSTRUMENTED_CALL_KW] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_END_FOR] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG },
    [INSTRUMENTED_END_SEND] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG },
    [INSTRUMENTED_FOR_ITER] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_INSTRUCTION] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_JUMP_BACKWARD] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG },
    [INSTRUMENTED_JUMP_FORWARD] = { true, INSTR_FMT_IB, HAS_ARG_FLAG },
    [INSTRUMENTED_LOAD_SUPER_ATTR] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG },
    [INSTRUMENTED_POP_JUMP_IF_FALSE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG },
    [INSTRUMENTED_POP_JUMP_IF_NONE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG },
    [INSTRUMENTED_POP_JUMP_IF_NOT_NONE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG },
    [INSTRUMENTED_POP_JUMP_IF_TRUE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG },
    [INSTRUMENTED_RESUME] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_RETURN_CONST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_CONST_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_RETURN_VALUE] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [INSTRUMENTED_YIELD_VALUE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [INTERPRETER_EXIT] = { true, INSTR_FMT_IX, HAS_ESCAPES_FLAG },
    [IS_OP] = { true, INSTR_FMT_IB, HAS_ARG_FLAG },
    [JUMP_BACKWARD] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG | HAS_EVAL_BREAK_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [JUMP_BACKWARD_NO_INTERRUPT] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_JUMP_FLAG },
    [JUMP_FORWARD] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_JUMP_FLAG },
    [LIST_APPEND] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG },
    [LIST_EXTEND] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_ASSERTION_ERROR] = { true, INSTR_FMT_IX, 0 },
    [LOAD_ATTR] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_ATTR_CLASS] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [LOAD_ATTR_GETATTRIBUTE_OVERRIDDEN] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_DEOPT_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_ATTR_INSTANCE_VALUE] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [LOAD_ATTR_METHOD_LAZY_DICT] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [LOAD_ATTR_METHOD_NO_DICT] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_EXIT_FLAG },
    [LOAD_ATTR_METHOD_WITH_VALUES] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [LOAD_ATTR_MODULE] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [LOAD_ATTR_NONDESCRIPTOR_NO_DICT] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_EXIT_FLAG },
    [LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [LOAD_ATTR_PROPERTY] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_ATTR_SLOT] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [LOAD_ATTR_WITH_HINT] = { true, INSTR_FMT_IBC00000000, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [LOAD_BUILD_CLASS] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_CONST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_CONST_FLAG | HAS_PURE_FLAG },
    [LOAD_DEREF] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_FREE_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_FAST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG | HAS_PURE_FLAG },
    [LOAD_FAST_AND_CLEAR] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG },
    [LOAD_FAST_CHECK] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_FAST_LOAD_FAST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG },
    [LOAD_FROM_DICT_OR_DEREF] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_FREE_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_FROM_DICT_OR_GLOBALS] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_GLOBAL] = { true, INSTR_FMT_IBC000, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_GLOBAL_BUILTIN] = { true, INSTR_FMT_IBC000, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [LOAD_GLOBAL_MODULE] = { true, INSTR_FMT_IBC000, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [LOAD_LOCALS] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_NAME] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_SUPER_ATTR] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_SUPER_ATTR_ATTR] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_SUPER_ATTR_METHOD] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [MAKE_CELL] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_FREE_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG },
    [MAKE_FUNCTION] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [MAP_ADD] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [MATCH_CLASS] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [MATCH_KEYS] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [MATCH_MAPPING] = { true, INSTR_FMT_IX, 0 },
    [MATCH_SEQUENCE] = { true, INSTR_FMT_IX, 0 },
    [NOP] = { true, INSTR_FMT_IX, HAS_PURE_FLAG },
    [POP_EXCEPT] = { true, INSTR_FMT_IX, HAS_ESCAPES_FLAG },
    [POP_JUMP_IF_FALSE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG },
    [POP_JUMP_IF_NONE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG },
    [POP_JUMP_IF_NOT_NONE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG },
    [POP_JUMP_IF_TRUE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG },
    [POP_TOP] = { true, INSTR_FMT_IX, HAS_PURE_FLAG },
    [PUSH_EXC_INFO] = { true, INSTR_FMT_IX, 0 },
    [PUSH_NULL] = { true, INSTR_FMT_IX, HAS_PURE_FLAG },
    [RAISE_VARARGS] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [RERAISE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [RESERVED] = { true, INSTR_FMT_IX, 0 },
    [RESUME] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_EVAL_BREAK_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [RESUME_CHECK] = { true, INSTR_FMT_IX, HAS_DEOPT_FLAG },
    [RETURN_CONST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_CONST_FLAG },
    [RETURN_GENERATOR] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [RETURN_VALUE] = { true, INSTR_FMT_IX, 0 },
    [SEND] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_JUMP_FLAG | HAS_ERROR_FLAG | HAS_ERROR_NO_POP_FLAG | HAS_ESCAPES_FLAG },
    [SEND_GEN] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [SETUP_ANNOTATIONS] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [SET_ADD] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [SET_FUNCTION_ATTRIBUTE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ESCAPES_FLAG },
    [SET_UPDATE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [STORE_ATTR] = { true, INSTR_FMT_IBC000, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [STORE_ATTR_INSTANCE_VALUE] = { true, INSTR_FMT_IXC000, HAS_DEOPT_FLAG | HAS_EXIT_FLAG },
    [STORE_ATTR_SLOT] = { true, INSTR_FMT_IXC000, HAS_EXIT_FLAG },
    [STORE_ATTR_WITH_HINT] = { true, INSTR_FMT_IBC000, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_DEOPT_FLAG | HAS_ESCAPES_FLAG },
    [STORE_DEREF] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_FREE_FLAG | HAS_ESCAPES_FLAG },
    [STORE_FAST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG },
    [STORE_FAST_LOAD_FAST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG },
    [STORE_FAST_STORE_FAST] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_LOCAL_FLAG },
    [STORE_GLOBAL] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [STORE_NAME] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [STORE_SLICE] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [STORE_SUBSCR] = { true, INSTR_FMT_IXC, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [STORE_SUBSCR_DICT] = { true, INSTR_FMT_IXC, HAS_DEOPT_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [STORE_SUBSCR_LIST_INT] = { true, INSTR_FMT_IXC, HAS_DEOPT_FLAG },
    [SWAP] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_PURE_FLAG },
    [TO_BOOL] = { true, INSTR_FMT_IXC00, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [TO_BOOL_ALWAYS_TRUE] = { true, INSTR_FMT_IXC00, HAS_EXIT_FLAG },
    [TO_BOOL_BOOL] = { true, INSTR_FMT_IXC00, HAS_EXIT_FLAG },
    [TO_BOOL_INT] = { true, INSTR_FMT_IXC00, HAS_EXIT_FLAG | HAS_ESCAPES_FLAG },
    [TO_BOOL_LIST] = { true, INSTR_FMT_IXC00, HAS_EXIT_FLAG },
    [TO_BOOL_NONE] = { true, INSTR_FMT_IXC00, HAS_EXIT_FLAG },
    [TO_BOOL_STR] = { true, INSTR_FMT_IXC00, HAS_EXIT_FLAG | HAS_ESCAPES_FLAG },
    [UNARY_INVERT] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [UNARY_NEGATIVE] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [UNARY_NOT] = { true, INSTR_FMT_IX, HAS_PURE_FLAG },
    [UNPACK_EX] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [UNPACK_SEQUENCE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [UNPACK_SEQUENCE_LIST] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [UNPACK_SEQUENCE_TUPLE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [UNPACK_SEQUENCE_TWO_TUPLE] = { true, INSTR_FMT_IBC, HAS_ARG_FLAG | HAS_DEOPT_FLAG },
    [WITH_EXCEPT_START] = { true, INSTR_FMT_IX, HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [YIELD_VALUE] = { true, INSTR_FMT_IB, HAS_ARG_FLAG | HAS_ESCAPES_FLAG },
    [JUMP] = { true, -1, HAS_ARG_FLAG | HAS_JUMP_FLAG | HAS_EVAL_BREAK_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [JUMP_NO_INTERRUPT] = { true, -1, HAS_ARG_FLAG | HAS_JUMP_FLAG },
    [LOAD_CLOSURE] = { true, -1, HAS_ARG_FLAG | HAS_LOCAL_FLAG | HAS_PURE_FLAG },
    [LOAD_METHOD] = { true, -1, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_SUPER_METHOD] = { true, -1, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_ZERO_SUPER_ATTR] = { true, -1, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [LOAD_ZERO_SUPER_METHOD] = { true, -1, HAS_ARG_FLAG | HAS_NAME_FLAG | HAS_ERROR_FLAG | HAS_ESCAPES_FLAG },
    [POP_BLOCK] = { true, -1, HAS_PURE_FLAG },
    [SETUP_CLEANUP] = { true, -1, HAS_PURE_FLAG | HAS_ARG_FLAG },
    [SETUP_FINALLY] = { true, -1, HAS_PURE_FLAG | HAS_ARG_FLAG },
    [SETUP_WITH] = { true, -1, HAS_PURE_FLAG | HAS_ARG_FLAG },
    [STORE_FAST_MAYBE_NULL] = { true, -1, HAS_ARG_FLAG | HAS_LOCAL_FLAG },
};
#endif

#define MAX_UOP_PER_EXPANSION 8
struct opcode_macro_expansion {
    int nuops;
    struct { int16_t uop; int8_t size; int8_t offset; } uops[MAX_UOP_PER_EXPANSION];
};
extern const struct opcode_macro_expansion _PyOpcode_macro_expansion[256];

#ifdef NEED_OPCODE_METADATA
const struct opcode_macro_expansion
_PyOpcode_macro_expansion[256] = {
    [BINARY_OP] = { .nuops = 1, .uops = { { _BINARY_OP, 0, 0 } } },
    [BINARY_OP_ADD_FLOAT] = { .nuops = 2, .uops = { { _GUARD_BOTH_FLOAT, 0, 0 }, { _BINARY_OP_ADD_FLOAT, 0, 0 } } },
    [BINARY_OP_ADD_INT] = { .nuops = 2, .uops = { { _GUARD_BOTH_INT, 0, 0 }, { _BINARY_OP_ADD_INT, 0, 0 } } },
    [BINARY_OP_ADD_UNICODE] = { .nuops = 2, .uops = { { _GUARD_BOTH_UNICODE, 0, 0 }, { _BINARY_OP_ADD_UNICODE, 0, 0 } } },
    [BINARY_OP_MULTIPLY_FLOAT] = { .nuops = 2, .uops = { { _GUARD_BOTH_FLOAT, 0, 0 }, { _BINARY_OP_MULTIPLY_FLOAT, 0, 0 } } },
    [BINARY_OP_MULTIPLY_INT] = { .nuops = 2, .uops = { { _GUARD_BOTH_INT, 0, 0 }, { _BINARY_OP_MULTIPLY_INT, 0, 0 } } },
    [BINARY_OP_SUBTRACT_FLOAT] = { .nuops = 2, .uops = { { _GUARD_BOTH_FLOAT, 0, 0 }, { _BINARY_OP_SUBTRACT_FLOAT, 0, 0 } } },
    [BINARY_OP_SUBTRACT_INT] = { .nuops = 2, .uops = { { _GUARD_BOTH_INT, 0, 0 }, { _BINARY_OP_SUBTRACT_INT, 0, 0 } } },
    [BINARY_SLICE] = { .nuops = 1, .uops = { { _BINARY_SLICE, 0, 0 } } },
    [BINARY_SUBSCR] = { .nuops = 1, .uops = { { _BINARY_SUBSCR, 0, 0 } } },
    [BINARY_SUBSCR_DICT] = { .nuops = 1, .uops = { { _BINARY_SUBSCR_DICT, 0, 0 } } },
    [BINARY_SUBSCR_LIST_INT] = { .nuops = 1, .uops = { { _BINARY_SUBSCR_LIST_INT, 0, 0 } } },
    [BINARY_SUBSCR_STR_INT] = { .nuops = 1, .uops = { { _BINARY_SUBSCR_STR_INT, 0, 0 } } },
    [BINARY_SUBSCR_TUPLE_INT] = { .nuops = 1, .uops = { { _BINARY_SUBSCR_TUPLE_INT, 0, 0 } } },
    [BUILD_CONST_KEY_MAP] = { .nuops = 1, .uops = { { _BUILD_CONST_KEY_MAP, 0, 0 } } },
    [BUILD_LIST] = { .nuops = 1, .uops = { { _BUILD_LIST, 0, 0 } } },
    [BUILD_MAP] = { .nuops = 1, .uops = { { _BUILD_MAP, 0, 0 } } },
    [BUILD_SLICE] = { .nuops = 1, .uops = { { _BUILD_SLICE, 0, 0 } } },
    [BUILD_STRING] = { .nuops = 1, .uops = { { _BUILD_STRING, 0, 0 } } },
    [BUILD_TUPLE] = { .nuops = 1, .uops = { { _BUILD_TUPLE, 0, 0 } } },
    [CALL_BOUND_METHOD_EXACT_ARGS] = { .nuops = 8, .uops = { { _CHECK_PEP_523, 0, 0 }, { _CHECK_CALL_BOUND_METHOD_EXACT_ARGS, 0, 0 }, { _INIT_CALL_BOUND_METHOD_EXACT_ARGS, 0, 0 }, { _CHECK_FUNCTION_EXACT_ARGS, 2, 1 }, { _CHECK_STACK_SPACE, 0, 0 }, { _INIT_CALL_PY_EXACT_ARGS, 0, 0 }, { _SAVE_RETURN_OFFSET, 7, 3 }, { _PUSH_FRAME, 0, 0 } } },
    [CALL_BOUND_METHOD_GENERAL] = { .nuops = 6, .uops = { { _CHECK_PEP_523, 0, 0 }, { _CHECK_METHOD_VERSION, 2, 1 }, { _EXPAND_METHOD, 0, 0 }, { _PY_FRAME_GENERAL, 0, 0 }, { _SAVE_RETURN_OFFSET, 7, 3 }, { _PUSH_FRAME, 0, 0 } } },
    [CALL_BUILTIN_CLASS] = { .nuops = 2, .uops = { { _CALL_BUILTIN_CLASS, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_BUILTIN_FAST] = { .nuops = 2, .uops = { { _CALL_BUILTIN_FAST, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_BUILTIN_FAST_WITH_KEYWORDS] = { .nuops = 2, .uops = { { _CALL_BUILTIN_FAST_WITH_KEYWORDS, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_BUILTIN_O] = { .nuops = 2, .uops = { { _CALL_BUILTIN_O, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_INTRINSIC_1] = { .nuops = 1, .uops = { { _CALL_INTRINSIC_1, 0, 0 } } },
    [CALL_INTRINSIC_2] = { .nuops = 1, .uops = { { _CALL_INTRINSIC_2, 0, 0 } } },
    [CALL_ISINSTANCE] = { .nuops = 1, .uops = { { _CALL_ISINSTANCE, 0, 0 } } },
    [CALL_LEN] = { .nuops = 1, .uops = { { _CALL_LEN, 0, 0 } } },
    [CALL_METHOD_DESCRIPTOR_FAST] = { .nuops = 2, .uops = { { _CALL_METHOD_DESCRIPTOR_FAST, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS] = { .nuops = 2, .uops = { { _CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_METHOD_DESCRIPTOR_NOARGS] = { .nuops = 2, .uops = { { _CALL_METHOD_DESCRIPTOR_NOARGS, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_METHOD_DESCRIPTOR_O] = { .nuops = 2, .uops = { { _CALL_METHOD_DESCRIPTOR_O, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_NON_PY_GENERAL] = { .nuops = 3, .uops = { { _CHECK_IS_NOT_PY_CALLABLE, 0, 0 }, { _CALL_NON_PY_GENERAL, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_PY_EXACT_ARGS] = { .nuops = 6, .uops = { { _CHECK_PEP_523, 0, 0 }, { _CHECK_FUNCTION_EXACT_ARGS, 2, 1 }, { _CHECK_STACK_SPACE, 0, 0 }, { _INIT_CALL_PY_EXACT_ARGS, 0, 0 }, { _SAVE_RETURN_OFFSET, 7, 3 }, { _PUSH_FRAME, 0, 0 } } },
    [CALL_PY_GENERAL] = { .nuops = 5, .uops = { { _CHECK_PEP_523, 0, 0 }, { _CHECK_FUNCTION_VERSION, 2, 1 }, { _PY_FRAME_GENERAL, 0, 0 }, { _SAVE_RETURN_OFFSET, 7, 3 }, { _PUSH_FRAME, 0, 0 } } },
    [CALL_STR_1] = { .nuops = 2, .uops = { { _CALL_STR_1, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_TUPLE_1] = { .nuops = 2, .uops = { { _CALL_TUPLE_1, 0, 0 }, { _CHECK_PERIODIC, 0, 0 } } },
    [CALL_TYPE_1] = { .nuops = 1, .uops = { { _CALL_TYPE_1, 0, 0 } } },
    [CHECK_EG_MATCH] = { .nuops = 1, .uops = { { _CHECK_EG_MATCH, 0, 0 } } },
    [CHECK_EXC_MATCH] = { .nuops = 1, .uops = { { _CHECK_EXC_MATCH, 0, 0 } } },
    [COMPARE_OP] = { .nuops = 1, .uops = { { _COMPARE_OP, 0, 0 } } },
    [COMPARE_OP_FLOAT] = { .nuops = 2, .uops = { { _GUARD_BOTH_FLOAT, 0, 0 }, { _COMPARE_OP_FLOAT, 0, 0 } } },
    [COMPARE_OP_INT] = { .nuops = 2, .uops = { { _GUARD_BOTH_INT, 0, 0 }, { _COMPARE_OP_INT, 0, 0 } } },
    [COMPARE_OP_STR] = { .nuops = 2, .uops = { { _GUARD_BOTH_UNICODE, 0, 0 }, { _COMPARE_OP_STR, 0, 0 } } },
    [CONTAINS_OP] = { .nuops = 1, .uops = { { _CONTAINS_OP, 0, 0 } } },
    [CONTAINS_OP_DICT] = { .nuops = 1, .uops = { { _CONTAINS_OP_DICT, 0, 0 } } },
    [CONTAINS_OP_SET] = { .nuops = 1, .uops = { { _CONTAINS_OP_SET, 0, 0 } } },
    [CONVERT_VALUE] = { .nuops = 1, .uops = { { _CONVERT_VALUE, 0, 0 } } },
    [COPY] = { .nuops = 1, .uops = { { _COPY, 0, 0 } } },
    [COPY_FREE_VARS] = { .nuops = 1, .uops = { { _COPY_FREE_VARS, 0, 0 } } },
    [DELETE_ATTR] = { .nuops = 1, .uops = { { _DELETE_ATTR, 0, 0 } } },
    [DELETE_DEREF] = { .nuops = 1, .uops = { { _DELETE_DEREF, 0, 0 } } },
    [DELETE_FAST] = { .nuops = 1, .uops = { { _DELETE_FAST, 0, 0 } } },
    [DELETE_GLOBAL] = { .nuops = 1, .uops = { { _DELETE_GLOBAL, 0, 0 } } },
    [DELETE_NAME] = { .nuops = 1, .uops = { { _DELETE_NAME, 0, 0 } } },
    [DELETE_SUBSCR] = { .nuops = 1, .uops = { { _DELETE_SUBSCR, 0, 0 } } },
    [DICT_MERGE] = { .nuops = 1, .uops = { { _DICT_MERGE, 0, 0 } } },
    [DICT_UPDATE] = { .nuops = 1, .uops = { { _DICT_UPDATE, 0, 0 } } },
    [END_FOR] = { .nuops = 1, .uops = { { _POP_TOP, 0, 0 } } },
    [END_SEND] = { .nuops = 1, .uops = { { _END_SEND, 0, 0 } } },
    [EXIT_INIT_CHECK] = { .nuops = 1, .uops = { { _EXIT_INIT_CHECK, 0, 0 } } },
    [FORMAT_SIMPLE] = { .nuops = 1, .uops = { { _FORMAT_SIMPLE, 0, 0 } } },
    [FORMAT_WITH_SPEC] = { .nuops = 1, .uops = { { _FORMAT_WITH_SPEC, 0, 0 } } },
    [FOR_ITER] = { .nuops = 1, .uops = { { _FOR_ITER, 9, 0 } } },
    [FOR_ITER_GEN] = { .nuops = 3, .uops = { { _CHECK_PEP_523, 0, 0 }, { _FOR_ITER_GEN_FRAME, 0, 0 }, { _PUSH_FRAME, 0, 0 } } },
    [FOR_ITER_LIST] = { .nuops = 3, .uops = { { _ITER_CHECK_LIST, 0, 0 }, { _ITER_JUMP_LIST, 9, 1 }, { _ITER_NEXT_LIST, 0, 0 } } },
    [FOR_ITER_RANGE] = { .nuops = 3, .uops = { { _ITER_CHECK_RANGE, 0, 0 }, { _ITER_JUMP_RANGE, 9, 1 }, { _ITER_NEXT_RANGE, 0, 0 } } },
    [FOR_ITER_TUPLE] = { .nuops = 3, .uops = { { _ITER_CHECK_TUPLE, 0, 0 }, { _ITER_JUMP_TUPLE, 9, 1 }, { _ITER_NEXT_TUPLE, 0, 0 } } },
    [GET_AITER] = { .nuops = 1, .uops = { { _GET_AITER, 0, 0 } } },
    [GET_ANEXT] = { .nuops = 1, .uops = { { _GET_ANEXT, 0, 0 } } },
    [GET_AWAITABLE] = { .nuops = 1, .uops = { { _GET_AWAITABLE, 0, 0 } } },
    [GET_ITER] = { .nuops = 1, .uops = { { _GET_ITER, 0, 0 } } },
    [GET_LEN] = { .nuops = 1, .uops = { { _GET_LEN, 0, 0 } } },
    [GET_YIELD_FROM_ITER] = { .nuops = 1, .uops = { { _GET_YIELD_FROM_ITER, 0, 0 } } },
    [IS_OP] = { .nuops = 1, .uops = { { _IS_OP, 0, 0 } } },
    [LIST_APPEND] = { .nuops = 1, .uops = { { _LIST_APPEND, 0, 0 } } },
    [LIST_EXTEND] = { .nuops = 1, .uops = { { _LIST_EXTEND, 0, 0 } } },
    [LOAD_ASSERTION_ERROR] = { .nuops = 1, .uops = { { _LOAD_ASSERTION_ERROR, 0, 0 } } },
    [LOAD_ATTR] = { .nuops = 1, .uops = { { _LOAD_ATTR, 0, 0 } } },
    [LOAD_ATTR_CLASS] = { .nuops = 2, .uops = { { _CHECK_ATTR_CLASS, 2, 1 }, { _LOAD_ATTR_CLASS, 4, 5 } } },
    [LOAD_ATTR_INSTANCE_VALUE] = { .nuops = 3, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _CHECK_MANAGED_OBJECT_HAS_VALUES, 0, 0 }, { _LOAD_ATTR_INSTANCE_VALUE, 1, 3 } } },
    [LOAD_ATTR_METHOD_LAZY_DICT] = { .nuops = 3, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _CHECK_ATTR_METHOD_LAZY_DICT, 1, 3 }, { _LOAD_ATTR_METHOD_LAZY_DICT, 4, 5 } } },
    [LOAD_ATTR_METHOD_NO_DICT] = { .nuops = 2, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _LOAD_ATTR_METHOD_NO_DICT, 4, 5 } } },
    [LOAD_ATTR_METHOD_WITH_VALUES] = { .nuops = 4, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _GUARD_DORV_VALUES_INST_ATTR_FROM_DICT, 0, 0 }, { _GUARD_KEYS_VERSION, 2, 3 }, { _LOAD_ATTR_METHOD_WITH_VALUES, 4, 5 } } },
    [LOAD_ATTR_MODULE] = { .nuops = 2, .uops = { { _CHECK_ATTR_MODULE, 2, 1 }, { _LOAD_ATTR_MODULE, 1, 3 } } },
    [LOAD_ATTR_NONDESCRIPTOR_NO_DICT] = { .nuops = 2, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _LOAD_ATTR_NONDESCRIPTOR_NO_DICT, 4, 5 } } },
    [LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES] = { .nuops = 4, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _GUARD_DORV_VALUES_INST_ATTR_FROM_DICT, 0, 0 }, { _GUARD_KEYS_VERSION, 2, 3 }, { _LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES, 4, 5 } } },
    [LOAD_ATTR_SLOT] = { .nuops = 2, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _LOAD_ATTR_SLOT, 1, 3 } } },
    [LOAD_ATTR_WITH_HINT] = { .nuops = 3, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _CHECK_ATTR_WITH_HINT, 0, 0 }, { _LOAD_ATTR_WITH_HINT, 1, 3 } } },
    [LOAD_BUILD_CLASS] = { .nuops = 1, .uops = { { _LOAD_BUILD_CLASS, 0, 0 } } },
    [LOAD_CONST] = { .nuops = 1, .uops = { { _LOAD_CONST, 0, 0 } } },
    [LOAD_DEREF] = { .nuops = 1, .uops = { { _LOAD_DEREF, 0, 0 } } },
    [LOAD_FAST] = { .nuops = 1, .uops = { { _LOAD_FAST, 0, 0 } } },
    [LOAD_FAST_AND_CLEAR] = { .nuops = 1, .uops = { { _LOAD_FAST_AND_CLEAR, 0, 0 } } },
    [LOAD_FAST_CHECK] = { .nuops = 1, .uops = { { _LOAD_FAST_CHECK, 0, 0 } } },
    [LOAD_FAST_LOAD_FAST] = { .nuops = 2, .uops = { { _LOAD_FAST, 5, 0 }, { _LOAD_FAST, 6, 0 } } },
    [LOAD_FROM_DICT_OR_DEREF] = { .nuops = 1, .uops = { { _LOAD_FROM_DICT_OR_DEREF, 0, 0 } } },
    [LOAD_GLOBAL] = { .nuops = 1, .uops = { { _LOAD_GLOBAL, 0, 0 } } },
    [LOAD_GLOBAL_BUILTIN] = { .nuops = 3, .uops = { { _GUARD_GLOBALS_VERSION, 1, 1 }, { _GUARD_BUILTINS_VERSION, 1, 2 }, { _LOAD_GLOBAL_BUILTINS, 1, 3 } } },
    [LOAD_GLOBAL_MODULE] = { .nuops = 2, .uops = { { _GUARD_GLOBALS_VERSION, 1, 1 }, { _LOAD_GLOBAL_MODULE, 1, 3 } } },
    [LOAD_LOCALS] = { .nuops = 1, .uops = { { _LOAD_LOCALS, 0, 0 } } },
    [LOAD_SUPER_ATTR_ATTR] = { .nuops = 1, .uops = { { _LOAD_SUPER_ATTR_ATTR, 0, 0 } } },
    [LOAD_SUPER_ATTR_METHOD] = { .nuops = 1, .uops = { { _LOAD_SUPER_ATTR_METHOD, 0, 0 } } },
    [MAKE_CELL] = { .nuops = 1, .uops = { { _MAKE_CELL, 0, 0 } } },
    [MAKE_FUNCTION] = { .nuops = 1, .uops = { { _MAKE_FUNCTION, 0, 0 } } },
    [MAP_ADD] = { .nuops = 1, .uops = { { _MAP_ADD, 0, 0 } } },
    [MATCH_CLASS] = { .nuops = 1, .uops = { { _MATCH_CLASS, 0, 0 } } },
    [MATCH_KEYS] = { .nuops = 1, .uops = { { _MATCH_KEYS, 0, 0 } } },
    [MATCH_MAPPING] = { .nuops = 1, .uops = { { _MATCH_MAPPING, 0, 0 } } },
    [MATCH_SEQUENCE] = { .nuops = 1, .uops = { { _MATCH_SEQUENCE, 0, 0 } } },
    [NOP] = { .nuops = 1, .uops = { { _NOP, 0, 0 } } },
    [POP_EXCEPT] = { .nuops = 1, .uops = { { _POP_EXCEPT, 0, 0 } } },
    [POP_JUMP_IF_FALSE] = { .nuops = 1, .uops = { { _POP_JUMP_IF_FALSE, 9, 1 } } },
    [POP_JUMP_IF_NONE] = { .nuops = 2, .uops = { { _IS_NONE, 0, 0 }, { _POP_JUMP_IF_TRUE, 9, 1 } } },
    [POP_JUMP_IF_NOT_NONE] = { .nuops = 2, .uops = { { _IS_NONE, 0, 0 }, { _POP_JUMP_IF_FALSE, 9, 1 } } },
    [POP_JUMP_IF_TRUE] = { .nuops = 1, .uops = { { _POP_JUMP_IF_TRUE, 9, 1 } } },
    [POP_TOP] = { .nuops = 1, .uops = { { _POP_TOP, 0, 0 } } },
    [PUSH_EXC_INFO] = { .nuops = 1, .uops = { { _PUSH_EXC_INFO, 0, 0 } } },
    [PUSH_NULL] = { .nuops = 1, .uops = { { _PUSH_NULL, 0, 0 } } },
    [RESUME_CHECK] = { .nuops = 1, .uops = { { _RESUME_CHECK, 0, 0 } } },
    [RETURN_CONST] = { .nuops = 2, .uops = { { _LOAD_CONST, 0, 0 }, { _POP_FRAME, 0, 0 } } },
    [RETURN_GENERATOR] = { .nuops = 1, .uops = { { _RETURN_GENERATOR, 0, 0 } } },
    [RETURN_VALUE] = { .nuops = 1, .uops = { { _POP_FRAME, 0, 0 } } },
    [SETUP_ANNOTATIONS] = { .nuops = 1, .uops = { { _SETUP_ANNOTATIONS, 0, 0 } } },
    [SET_ADD] = { .nuops = 1, .uops = { { _SET_ADD, 0, 0 } } },
    [SET_FUNCTION_ATTRIBUTE] = { .nuops = 1, .uops = { { _SET_FUNCTION_ATTRIBUTE, 0, 0 } } },
    [SET_UPDATE] = { .nuops = 1, .uops = { { _SET_UPDATE, 0, 0 } } },
    [STORE_ATTR] = { .nuops = 1, .uops = { { _STORE_ATTR, 0, 0 } } },
    [STORE_ATTR_INSTANCE_VALUE] = { .nuops = 3, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _GUARD_DORV_NO_DICT, 0, 0 }, { _STORE_ATTR_INSTANCE_VALUE, 1, 3 } } },
    [STORE_ATTR_SLOT] = { .nuops = 2, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _STORE_ATTR_SLOT, 1, 3 } } },
    [STORE_DEREF] = { .nuops = 1, .uops = { { _STORE_DEREF, 0, 0 } } },
    [STORE_FAST] = { .nuops = 1, .uops = { { _STORE_FAST, 0, 0 } } },
    [STORE_FAST_LOAD_FAST] = { .nuops = 2, .uops = { { _STORE_FAST, 5, 0 }, { _LOAD_FAST, 6, 0 } } },
    [STORE_FAST_STORE_FAST] = { .nuops = 2, .uops = { { _STORE_FAST, 5, 0 }, { _STORE_FAST, 6, 0 } } },
    [STORE_GLOBAL] = { .nuops = 1, .uops = { { _STORE_GLOBAL, 0, 0 } } },
    [STORE_NAME] = { .nuops = 1, .uops = { { _STORE_NAME, 0, 0 } } },
    [STORE_SLICE] = { .nuops = 1, .uops = { { _STORE_SLICE, 0, 0 } } },
    [STORE_SUBSCR] = { .nuops = 1, .uops = { { _STORE_SUBSCR, 0, 0 } } },
    [STORE_SUBSCR_DICT] = { .nuops = 1, .uops = { { _STORE_SUBSCR_DICT, 0, 0 } } },
    [STORE_SUBSCR_LIST_INT] = { .nuops = 1, .uops = { { _STORE_SUBSCR_LIST_INT, 0, 0 } } },
    [SWAP] = { .nuops = 1, .uops = { { _SWAP, 0, 0 } } },
    [TO_BOOL] = { .nuops = 1, .uops = { { _TO_BOOL, 0, 0 } } },
    [TO_BOOL_ALWAYS_TRUE] = { .nuops = 2, .uops = { { _GUARD_TYPE_VERSION, 2, 1 }, { _REPLACE_WITH_TRUE, 0, 0 } } },
    [TO_BOOL_BOOL] = { .nuops = 1, .uops = { { _TO_BOOL_BOOL, 0, 0 } } },
    [TO_BOOL_INT] = { .nuops = 1, .uops = { { _TO_BOOL_INT, 0, 0 } } },
    [TO_BOOL_LIST] = { .nuops = 1, .uops = { { _TO_BOOL_LIST, 0, 0 } } },
    [TO_BOOL_NONE] = { .nuops = 1, .uops = { { _TO_BOOL_NONE, 0, 0 } } },
    [TO_BOOL_STR] = { .nuops = 1, .uops = { { _TO_BOOL_STR, 0, 0 } } },
    [UNARY_INVERT] = { .nuops = 1, .uops = { { _UNARY_INVERT, 0, 0 } } },
    [UNARY_NEGATIVE] = { .nuops = 1, .uops = { { _UNARY_NEGATIVE, 0, 0 } } },
    [UNARY_NOT] = { .nuops = 1, .uops = { { _UNARY_NOT, 0, 0 } } },
    [UNPACK_EX] = { .nuops = 1, .uops = { { _UNPACK_EX, 0, 0 } } },
    [UNPACK_SEQUENCE] = { .nuops = 1, .uops = { { _UNPACK_SEQUENCE, 0, 0 } } },
    [UNPACK_SEQUENCE_LIST] = { .nuops = 1, .uops = { { _UNPACK_SEQUENCE_LIST, 0, 0 } } },
    [UNPACK_SEQUENCE_TUPLE] = { .nuops = 1, .uops = { { _UNPACK_SEQUENCE_TUPLE, 0, 0 } } },
    [UNPACK_SEQUENCE_TWO_TUPLE] = { .nuops = 1, .uops = { { _UNPACK_SEQUENCE_TWO_TUPLE, 0, 0 } } },
    [WITH_EXCEPT_START] = { .nuops = 1, .uops = { { _WITH_EXCEPT_START, 0, 0 } } },
    [YIELD_VALUE] = { .nuops = 1, .uops = { { _YIELD_VALUE, 0, 0 } } },
};
#endif // NEED_OPCODE_METADATA

extern const char *_PyOpcode_OpName[268];
#ifdef NEED_OPCODE_METADATA
const char *_PyOpcode_OpName[268] = {
    [BEFORE_ASYNC_WITH] = "BEFORE_ASYNC_WITH",
    [BEFORE_WITH] = "BEFORE_WITH",
    [BINARY_OP] = "BINARY_OP",
    [BINARY_OP_ADD_FLOAT] = "BINARY_OP_ADD_FLOAT",
    [BINARY_OP_ADD_INT] = "BINARY_OP_ADD_INT",
    [BINARY_OP_ADD_UNICODE] = "BINARY_OP_ADD_UNICODE",
    [BINARY_OP_INPLACE_ADD_UNICODE] = "BINARY_OP_INPLACE_ADD_UNICODE",
    [BINARY_OP_MULTIPLY_FLOAT] = "BINARY_OP_MULTIPLY_FLOAT",
    [BINARY_OP_MULTIPLY_INT] = "BINARY_OP_MULTIPLY_INT",
    [BINARY_OP_SUBTRACT_FLOAT] = "BINARY_OP_SUBTRACT_FLOAT",
    [BINARY_OP_SUBTRACT_INT] = "BINARY_OP_SUBTRACT_INT",
    [BINARY_SLICE] = "BINARY_SLICE",
    [BINARY_SUBSCR] = "BINARY_SUBSCR",
    [BINARY_SUBSCR_DICT] = "BINARY_SUBSCR_DICT",
    [BINARY_SUBSCR_GETITEM] = "BINARY_SUBSCR_GETITEM",
    [BINARY_SUBSCR_LIST_INT] = "BINARY_SUBSCR_LIST_INT",
    [BINARY_SUBSCR_STR_INT] = "BINARY_SUBSCR_STR_INT",
    [BINARY_SUBSCR_TUPLE_INT] = "BINARY_SUBSCR_TUPLE_INT",
    [BUILD_CONST_KEY_MAP] = "BUILD_CONST_KEY_MAP",
    [BUILD_LIST] = "BUILD_LIST",
    [BUILD_MAP] = "BUILD_MAP",
    [BUILD_SET] = "BUILD_SET",
    [BUILD_SLICE] = "BUILD_SLICE",
    [BUILD_STRING] = "BUILD_STRING",
    [BUILD_TUPLE] = "BUILD_TUPLE",
    [CACHE] = "CACHE",
    [CALL] = "CALL",
    [CALL_ALLOC_AND_ENTER_INIT] = "CALL_ALLOC_AND_ENTER_INIT",
    [CALL_BOUND_METHOD_EXACT_ARGS] = "CALL_BOUND_METHOD_EXACT_ARGS",
    [CALL_BOUND_METHOD_GENERAL] = "CALL_BOUND_METHOD_GENERAL",
    [CALL_BUILTIN_CLASS] = "CALL_BUILTIN_CLASS",
    [CALL_BUILTIN_FAST] = "CALL_BUILTIN_FAST",
    [CALL_BUILTIN_FAST_WITH_KEYWORDS] = "CALL_BUILTIN_FAST_WITH_KEYWORDS",
    [CALL_BUILTIN_O] = "CALL_BUILTIN_O",
    [CALL_FUNCTION_EX] = "CALL_FUNCTION_EX",
    [CALL_INTRINSIC_1] = "CALL_INTRINSIC_1",
    [CALL_INTRINSIC_2] = "CALL_INTRINSIC_2",
    [CALL_ISINSTANCE] = "CALL_ISINSTANCE",
    [CALL_KW] = "CALL_KW",
    [CALL_LEN] = "CALL_LEN",
    [CALL_LIST_APPEND] = "CALL_LIST_APPEND",
    [CALL_METHOD_DESCRIPTOR_FAST] = "CALL_METHOD_DESCRIPTOR_FAST",
    [CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS] = "CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS",
    [CALL_METHOD_DESCRIPTOR_NOARGS] = "CALL_METHOD_DESCRIPTOR_NOARGS",
    [CALL_METHOD_DESCRIPTOR_O] = "CALL_METHOD_DESCRIPTOR_O",
    [CALL_NON_PY_GENERAL] = "CALL_NON_PY_GENERAL",
    [CALL_PY_EXACT_ARGS] = "CALL_PY_EXACT_ARGS",
    [CALL_PY_GENERAL] = "CALL_PY_GENERAL",
    [CALL_STR_1] = "CALL_STR_1",
    [CALL_TUPLE_1] = "CALL_TUPLE_1",
    [CALL_TYPE_1] = "CALL_TYPE_1",
    [CHECK_EG_MATCH] = "CHECK_EG_MATCH",
    [CHECK_EXC_MATCH] = "CHECK_EXC_MATCH",
    [CLEANUP_THROW] = "CLEANUP_THROW",
    [COMPARE_OP] = "COMPARE_OP",
    [COMPARE_OP_FLOAT] = "COMPARE_OP_FLOAT",
    [COMPARE_OP_INT] = "COMPARE_OP_INT",
    [COMPARE_OP_STR] = "COMPARE_OP_STR",
    [CONTAINS_OP] = "CONTAINS_OP",
    [CONTAINS_OP_DICT] = "CONTAINS_OP_DICT",
    [CONTAINS_OP_SET] = "CONTAINS_OP_SET",
    [CONVERT_VALUE] = "CONVERT_VALUE",
    [COPY] = "COPY",
    [COPY_FREE_VARS] = "COPY_FREE_VARS",
    [DELETE_ATTR] = "DELETE_ATTR",
    [DELETE_DEREF] = "DELETE_DEREF",
    [DELETE_FAST] = "DELETE_FAST",
    [DELETE_GLOBAL] = "DELETE_GLOBAL",
    [DELETE_NAME] = "DELETE_NAME",
    [DELETE_SUBSCR] = "DELETE_SUBSCR",
    [DICT_MERGE] = "DICT_MERGE",
    [DICT_UPDATE] = "DICT_UPDATE",
    [END_ASYNC_FOR] = "END_ASYNC_FOR",
    [END_FOR] = "END_FOR",
    [END_SEND] = "END_SEND",
    [ENTER_EXECUTOR] = "ENTER_EXECUTOR",
    [EXIT_INIT_CHECK] = "EXIT_INIT_CHECK",
    [EXTENDED_ARG] = "EXTENDED_ARG",
    [FORMAT_SIMPLE] = "FORMAT_SIMPLE",
    [FORMAT_WITH_SPEC] = "FORMAT_WITH_SPEC",
    [FOR_ITER] = "FOR_ITER",
    [FOR_ITER_GEN] = "FOR_ITER_GEN",
    [FOR_ITER_LIST] = "FOR_ITER_LIST",
    [FOR_ITER_RANGE] = "FOR_ITER_RANGE",
    [FOR_ITER_TUPLE] = "FOR_ITER_TUPLE",
    [GET_AITER] = "GET_AITER",
    [GET_ANEXT] = "GET_ANEXT",
    [GET_AWAITABLE] = "GET_AWAITABLE",
    [GET_ITER] = "GET_ITER",
    [GET_LEN] = "GET_LEN",
    [GET_YIELD_FROM_ITER] = "GET_YIELD_FROM_ITER",
    [IMPORT_FROM] = "IMPORT_FROM",
    [IMPORT_NAME] = "IMPORT_NAME",
    [INSTRUMENTED_CALL] = "INSTRUMENTED_CALL",
    [INSTRUMENTED_CALL_FUNCTION_EX] = "INSTRUMENTED_CALL_FUNCTION_EX",
    [INSTRUMENTED_CALL_KW] = "INSTRUMENTED_CALL_KW",
    [INSTRUMENTED_END_FOR] = "INSTRUMENTED_END_FOR",
    [INSTRUMENTED_END_SEND] = "INSTRUMENTED_END_SEND",
    [INSTRUMENTED_FOR_ITER] = "INSTRUMENTED_FOR_ITER",
    [INSTRUMENTED_INSTRUCTION] = "INSTRUMENTED_INSTRUCTION",
    [INSTRUMENTED_JUMP_BACKWARD] = "INSTRUMENTED_JUMP_BACKWARD",
    [INSTRUMENTED_JUMP_FORWARD] = "INSTRUMENTED_JUMP_FORWARD",
    [INSTRUMENTED_LINE] = "INSTRUMENTED_LINE",
    [INSTRUMENTED_LOAD_SUPER_ATTR] = "INSTRUMENTED_LOAD_SUPER_ATTR",
    [INSTRUMENTED_POP_JUMP_IF_FALSE] = "INSTRUMENTED_POP_JUMP_IF_FALSE",
    [INSTRUMENTED_POP_JUMP_IF_NONE] = "INSTRUMENTED_POP_JUMP_IF_NONE",
    [INSTRUMENTED_POP_JUMP_IF_NOT_NONE] = "INSTRUMENTED_POP_JUMP_IF_NOT_NONE",
    [INSTRUMENTED_POP_JUMP_IF_TRUE] = "INSTRUMENTED_POP_JUMP_IF_TRUE",
    [INSTRUMENTED_RESUME] = "INSTRUMENTED_RESUME",
    [INSTRUMENTED_RETURN_CONST] = "INSTRUMENTED_RETURN_CONST",
    [INSTRUMENTED_RETURN_VALUE] = "INSTRUMENTED_RETURN_VALUE",
    [INSTRUMENTED_YIELD_VALUE] = "INSTRUMENTED_YIELD_VALUE",
    [INTERPRETER_EXIT] = "INTERPRETER_EXIT",
    [IS_OP] = "IS_OP",
    [JUMP] = "JUMP",
    [JUMP_BACKWARD] = "JUMP_BACKWARD",
    [JUMP_BACKWARD_NO_INTERRUPT] = "JUMP_BACKWARD_NO_INTERRUPT",
    [JUMP_FORWARD] = "JUMP_FORWARD",
    [JUMP_NO_INTERRUPT] = "JUMP_NO_INTERRUPT",
    [LIST_APPEND] = "LIST_APPEND",
    [LIST_EXTEND] = "LIST_EXTEND",
    [LOAD_ASSERTION_ERROR] = "LOAD_ASSERTION_ERROR",
    [LOAD_ATTR] = "LOAD_ATTR",
    [LOAD_ATTR_CLASS] = "LOAD_ATTR_CLASS",
    [LOAD_ATTR_GETATTRIBUTE_OVERRIDDEN] = "LOAD_ATTR_GETATTRIBUTE_OVERRIDDEN",
    [LOAD_ATTR_INSTANCE_VALUE] = "LOAD_ATTR_INSTANCE_VALUE",
    [LOAD_ATTR_METHOD_LAZY_DICT] = "LOAD_ATTR_METHOD_LAZY_DICT",
    [LOAD_ATTR_METHOD_NO_DICT] = "LOAD_ATTR_METHOD_NO_DICT",
    [LOAD_ATTR_METHOD_WITH_VALUES] = "LOAD_ATTR_METHOD_WITH_VALUES",
    [LOAD_ATTR_MODULE] = "LOAD_ATTR_MODULE",
    [LOAD_ATTR_NONDESCRIPTOR_NO_DICT] = "LOAD_ATTR_NONDESCRIPTOR_NO_DICT",
    [LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES] = "LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES",
    [LOAD_ATTR_PROPERTY] = "LOAD_ATTR_PROPERTY",
    [LOAD_ATTR_SLOT] = "LOAD_ATTR_SLOT",
    [LOAD_ATTR_WITH_HINT] = "LOAD_ATTR_WITH_HINT",
    [LOAD_BUILD_CLASS] = "LOAD_BUILD_CLASS",
    [LOAD_CLOSURE] = "LOAD_CLOSURE",
    [LOAD_CONST] = "LOAD_CONST",
    [LOAD_DEREF] = "LOAD_DEREF",
    [LOAD_FAST] = "LOAD_FAST",
    [LOAD_FAST_AND_CLEAR] = "LOAD_FAST_AND_CLEAR",
    [LOAD_FAST_CHECK] = "LOAD_FAST_CHECK",
    [LOAD_FAST_LOAD_FAST] = "LOAD_FAST_LOAD_FAST",
    [LOAD_FROM_DICT_OR_DEREF] = "LOAD_FROM_DICT_OR_DEREF",
    [LOAD_FROM_DICT_OR_GLOBALS] = "LOAD_FROM_DICT_OR_GLOBALS",
    [LOAD_GLOBAL] = "LOAD_GLOBAL",
    [LOAD_GLOBAL_BUILTIN] = "LOAD_GLOBAL_BUILTIN",
    [LOAD_GLOBAL_MODULE] = "LOAD_GLOBAL_MODULE",
    [LOAD_LOCALS] = "LOAD_LOCALS",
    [LOAD_METHOD] = "LOAD_METHOD",
    [LOAD_NAME] = "LOAD_NAME",
    [LOAD_SUPER_ATTR] = "LOAD_SUPER_ATTR",
    [LOAD_SUPER_ATTR_ATTR] = "LOAD_SUPER_ATTR_ATTR",
    [LOAD_SUPER_ATTR_METHOD] = "LOAD_SUPER_ATTR_METHOD",
    [LOAD_SUPER_METHOD] = "LOAD_SUPER_METHOD",
    [LOAD_ZERO_SUPER_ATTR] = "LOAD_ZERO_SUPER_ATTR",
    [LOAD_ZERO_SUPER_METHOD] = "LOAD_ZERO_SUPER_METHOD",
    [MAKE_CELL] = "MAKE_CELL",
    [MAKE_FUNCTION] = "MAKE_FUNCTION",
    [MAP_ADD] = "MAP_ADD",
    [MATCH_CLASS] = "MATCH_CLASS",
    [MATCH_KEYS] = "MATCH_KEYS",
    [MATCH_MAPPING] = "MATCH_MAPPING",
    [MATCH_SEQUENCE] = "MATCH_SEQUENCE",
    [NOP] = "NOP",
    [POP_BLOCK] = "POP_BLOCK",
    [POP_EXCEPT] = "POP_EXCEPT",
    [POP_JUMP_IF_FALSE] = "POP_JUMP_IF_FALSE",
    [POP_JUMP_IF_NONE] = "POP_JUMP_IF_NONE",
    [POP_JUMP_IF_NOT_NONE] = "POP_JUMP_IF_NOT_NONE",
    [POP_JUMP_IF_TRUE] = "POP_JUMP_IF_TRUE",
    [POP_TOP] = "POP_TOP",
    [PUSH_EXC_INFO] = "PUSH_EXC_INFO",
    [PUSH_NULL] = "PUSH_NULL",
    [RAISE_VARARGS] = "RAISE_VARARGS",
    [RERAISE] = "RERAISE",
    [RESERVED] = "RESERVED",
    [RESUME] = "RESUME",
    [RESUME_CHECK] = "RESUME_CHECK",
    [RETURN_CONST] = "RETURN_CONST",
    [RETURN_GENERATOR] = "RETURN_GENERATOR",
    [RETURN_VALUE] = "RETURN_VALUE",
    [SEND] = "SEND",
    [SEND_GEN] = "SEND_GEN",
    [SETUP_ANNOTATIONS] = "SETUP_ANNOTATIONS",
    [SETUP_CLEANUP] = "SETUP_CLEANUP",
    [SETUP_FINALLY] = "SETUP_FINALLY",
    [SETUP_WITH] = "SETUP_WITH",
    [SET_ADD] = "SET_ADD",
    [SET_FUNCTION_ATTRIBUTE] = "SET_FUNCTION_ATTRIBUTE",
    [SET_UPDATE] = "SET_UPDATE",
    [STORE_ATTR] = "STORE_ATTR",
    [STORE_ATTR_INSTANCE_VALUE] = "STORE_ATTR_INSTANCE_VALUE",
    [STORE_ATTR_SLOT] = "STORE_ATTR_SLOT",
    [STORE_ATTR_WITH_HINT] = "STORE_ATTR_WITH_HINT",
    [STORE_DEREF] = "STORE_DEREF",
    [STORE_FAST] = "STORE_FAST",
    [STORE_FAST_LOAD_FAST] = "STORE_FAST_LOAD_FAST",
    [STORE_FAST_MAYBE_NULL] = "STORE_FAST_MAYBE_NULL",
    [STORE_FAST_STORE_FAST] = "STORE_FAST_STORE_FAST",
    [STORE_GLOBAL] = "STORE_GLOBAL",
    [STORE_NAME] = "STORE_NAME",
    [STORE_SLICE] = "STORE_SLICE",
    [STORE_SUBSCR] = "STORE_SUBSCR",
    [STORE_SUBSCR_DICT] = "STORE_SUBSCR_DICT",
    [STORE_SUBSCR_LIST_INT] = "STORE_SUBSCR_LIST_INT",
    [SWAP] = "SWAP",
    [TO_BOOL] = "TO_BOOL",
    [TO_BOOL_ALWAYS_TRUE] = "TO_BOOL_ALWAYS_TRUE",
    [TO_BOOL_BOOL] = "TO_BOOL_BOOL",
    [TO_BOOL_INT] = "TO_BOOL_INT",
    [TO_BOOL_LIST] = "TO_BOOL_LIST",
    [TO_BOOL_NONE] = "TO_BOOL_NONE",
    [TO_BOOL_STR] = "TO_BOOL_STR",
    [UNARY_INVERT] = "UNARY_INVERT",
    [UNARY_NEGATIVE] = "UNARY_NEGATIVE",
    [UNARY_NOT] = "UNARY_NOT",
    [UNPACK_EX] = "UNPACK_EX",
    [UNPACK_SEQUENCE] = "UNPACK_SEQUENCE",
    [UNPACK_SEQUENCE_LIST] = "UNPACK_SEQUENCE_LIST",
    [UNPACK_SEQUENCE_TUPLE] = "UNPACK_SEQUENCE_TUPLE",
    [UNPACK_SEQUENCE_TWO_TUPLE] = "UNPACK_SEQUENCE_TWO_TUPLE",
    [WITH_EXCEPT_START] = "WITH_EXCEPT_START",
    [YIELD_VALUE] = "YIELD_VALUE",
};
#endif

extern const uint8_t _PyOpcode_Caches[256];
#ifdef NEED_OPCODE_METADATA
const uint8_t _PyOpcode_Caches[256] = {
    [JUMP_BACKWARD] = 1,
    [TO_BOOL] = 3,
    [BINARY_SUBSCR] = 1,
    [STORE_SUBSCR] = 1,
    [SEND] = 1,
    [UNPACK_SEQUENCE] = 1,
    [STORE_ATTR] = 4,
    [LOAD_GLOBAL] = 4,
    [LOAD_SUPER_ATTR] = 1,
    [LOAD_ATTR] = 9,
    [COMPARE_OP] = 1,
    [CONTAINS_OP] = 1,
    [POP_JUMP_IF_TRUE] = 1,
    [POP_JUMP_IF_FALSE] = 1,
    [POP_JUMP_IF_NONE] = 1,
    [POP_JUMP_IF_NOT_NONE] = 1,
    [FOR_ITER] = 1,
    [CALL] = 3,
    [BINARY_OP] = 1,
};
#endif

extern const uint8_t _PyOpcode_Deopt[256];
#ifdef NEED_OPCODE_METADATA
const uint8_t _PyOpcode_Deopt[256] = {
    [BEFORE_ASYNC_WITH] = BEFORE_ASYNC_WITH,
    [BEFORE_WITH] = BEFORE_WITH,
    [BINARY_OP] = BINARY_OP,
    [BINARY_OP_ADD_FLOAT] = BINARY_OP,
    [BINARY_OP_ADD_INT] = BINARY_OP,
    [BINARY_OP_ADD_UNICODE] = BINARY_OP,
    [BINARY_OP_INPLACE_ADD_UNICODE] = BINARY_OP,
    [BINARY_OP_MULTIPLY_FLOAT] = BINARY_OP,
    [BINARY_OP_MULTIPLY_INT] = BINARY_OP,
    [BINARY_OP_SUBTRACT_FLOAT] = BINARY_OP,
    [BINARY_OP_SUBTRACT_INT] = BINARY_OP,
    [BINARY_SLICE] = BINARY_SLICE,
    [BINARY_SUBSCR] = BINARY_SUBSCR,
    [BINARY_SUBSCR_DICT] = BINARY_SUBSCR,
    [BINARY_SUBSCR_GETITEM] = BINARY_SUBSCR,
    [BINARY_SUBSCR_LIST_INT] = BINARY_SUBSCR,
    [BINARY_SUBSCR_STR_INT] = BINARY_SUBSCR,
    [BINARY_SUBSCR_TUPLE_INT] = BINARY_SUBSCR,
    [BUILD_CONST_KEY_MAP] = BUILD_CONST_KEY_MAP,
    [BUILD_LIST] = BUILD_LIST,
    [BUILD_MAP] = BUILD_MAP,
    [BUILD_SET] = BUILD_SET,
    [BUILD_SLICE] = BUILD_SLICE,
    [BUILD_STRING] = BUILD_STRING,
    [BUILD_TUPLE] = BUILD_TUPLE,
    [CACHE] = CACHE,
    [CALL] = CALL,
    [CALL_ALLOC_AND_ENTER_INIT] = CALL,
    [CALL_BOUND_METHOD_EXACT_ARGS] = CALL,
    [CALL_BOUND_METHOD_GENERAL] = CALL,
    [CALL_BUILTIN_CLASS] = CALL,
    [CALL_BUILTIN_FAST] = CALL,
    [CALL_BUILTIN_FAST_WITH_KEYWORDS] = CALL,
    [CALL_BUILTIN_O] = CALL,
    [CALL_FUNCTION_EX] = CALL_FUNCTION_EX,
    [CALL_INTRINSIC_1] = CALL_INTRINSIC_1,
    [CALL_INTRINSIC_2] = CALL_INTRINSIC_2,
    [CALL_ISINSTANCE] = CALL,
    [CALL_KW] = CALL_KW,
    [CALL_LEN] = CALL,
    [CALL_LIST_APPEND] = CALL,
    [CALL_METHOD_DESCRIPTOR_FAST] = CALL,
    [CALL_METHOD_DESCRIPTOR_FAST_WITH_KEYWORDS] = CALL,
    [CALL_METHOD_DESCRIPTOR_NOARGS] = CALL,
    [CALL_METHOD_DESCRIPTOR_O] = CALL,
    [CALL_NON_PY_GENERAL] = CALL,
    [CALL_PY_EXACT_ARGS] = CALL,
    [CALL_PY_GENERAL] = CALL,
    [CALL_STR_1] = CALL,
    [CALL_TUPLE_1] = CALL,
    [CALL_TYPE_1] = CALL,
    [CHECK_EG_MATCH] = CHECK_EG_MATCH,
    [CHECK_EXC_MATCH] = CHECK_EXC_MATCH,
    [CLEANUP_THROW] = CLEANUP_THROW,
    [COMPARE_OP] = COMPARE_OP,
    [COMPARE_OP_FLOAT] = COMPARE_OP,
    [COMPARE_OP_INT] = COMPARE_OP,
    [COMPARE_OP_STR] = COMPARE_OP,
    [CONTAINS_OP] = CONTAINS_OP,
    [CONTAINS_OP_DICT] = CONTAINS_OP,
    [CONTAINS_OP_SET] = CONTAINS_OP,
    [CONVERT_VALUE] = CONVERT_VALUE,
    [COPY] = COPY,
    [COPY_FREE_VARS] = COPY_FREE_VARS,
    [DELETE_ATTR] = DELETE_ATTR,
    [DELETE_DEREF] = DELETE_DEREF,
    [DELETE_FAST] = DELETE_FAST,
    [DELETE_GLOBAL] = DELETE_GLOBAL,
    [DELETE_NAME] = DELETE_NAME,
    [DELETE_SUBSCR] = DELETE_SUBSCR,
    [DICT_MERGE] = DICT_MERGE,
    [DICT_UPDATE] = DICT_UPDATE,
    [END_ASYNC_FOR] = END_ASYNC_FOR,
    [END_FOR] = END_FOR,
    [END_SEND] = END_SEND,
    [ENTER_EXECUTOR] = ENTER_EXECUTOR,
    [EXIT_INIT_CHECK] = EXIT_INIT_CHECK,
    [EXTENDED_ARG] = EXTENDED_ARG,
    [FORMAT_SIMPLE] = FORMAT_SIMPLE,
    [FORMAT_WITH_SPEC] = FORMAT_WITH_SPEC,
    [FOR_ITER] = FOR_ITER,
    [FOR_ITER_GEN] = FOR_ITER,
    [FOR_ITER_LIST] = FOR_ITER,
    [FOR_ITER_RANGE] = FOR_ITER,
    [FOR_ITER_TUPLE] = FOR_ITER,
    [GET_AITER] = GET_AITER,
    [GET_ANEXT] = GET_ANEXT,
    [GET_AWAITABLE] = GET_AWAITABLE,
    [GET_ITER] = GET_ITER,
    [GET_LEN] = GET_LEN,
    [GET_YIELD_FROM_ITER] = GET_YIELD_FROM_ITER,
    [IMPORT_FROM] = IMPORT_FROM,
    [IMPORT_NAME] = IMPORT_NAME,
    [INSTRUMENTED_CALL] = INSTRUMENTED_CALL,
    [INSTRUMENTED_CALL_FUNCTION_EX] = INSTRUMENTED_CALL_FUNCTION_EX,
    [INSTRUMENTED_CALL_KW] = INSTRUMENTED_CALL_KW,
    [INSTRUMENTED_END_FOR] = INSTRUMENTED_END_FOR,
    [INSTRUMENTED_END_SEND] = INSTRUMENTED_END_SEND,
    [INSTRUMENTED_FOR_ITER] = INSTRUMENTED_FOR_ITER,
    [INSTRUMENTED_INSTRUCTION] = INSTRUMENTED_INSTRUCTION,
    [INSTRUMENTED_JUMP_BACKWARD] = INSTRUMENTED_JUMP_BACKWARD,
    [INSTRUMENTED_JUMP_FORWARD] = INSTRUMENTED_JUMP_FORWARD,
    [INSTRUMENTED_LINE] = INSTRUMENTED_LINE,
    [INSTRUMENTED_LOAD_SUPER_ATTR] = INSTRUMENTED_LOAD_SUPER_ATTR,
    [INSTRUMENTED_POP_JUMP_IF_FALSE] = INSTRUMENTED_POP_JUMP_IF_FALSE,
    [INSTRUMENTED_POP_JUMP_IF_NONE] = INSTRUMENTED_POP_JUMP_IF_NONE,
    [INSTRUMENTED_POP_JUMP_IF_NOT_NONE] = INSTRUMENTED_POP_JUMP_IF_NOT_NONE,
    [INSTRUMENTED_POP_JUMP_IF_TRUE] = INSTRUMENTED_POP_JUMP_IF_TRUE,
    [INSTRUMENTED_RESUME] = INSTRUMENTED_RESUME,
    [INSTRUMENTED_RETURN_CONST] = INSTRUMENTED_RETURN_CONST,
    [INSTRUMENTED_RETURN_VALUE] = INSTRUMENTED_RETURN_VALUE,
    [INSTRUMENTED_YIELD_VALUE] = INSTRUMENTED_YIELD_VALUE,
    [INTERPRETER_EXIT] = INTERPRETER_EXIT,
    [IS_OP] = IS_OP,
    [JUMP_BACKWARD] = JUMP_BACKWARD,
    [JUMP_BACKWARD_NO_INTERRUPT] = JUMP_BACKWARD_NO_INTERRUPT,
    [JUMP_FORWARD] = JUMP_FORWARD,
    [LIST_APPEND] = LIST_APPEND,
    [LIST_EXTEND] = LIST_EXTEND,
    [LOAD_ASSERTION_ERROR] = LOAD_ASSERTION_ERROR,
    [LOAD_ATTR] = LOAD_ATTR,
    [LOAD_ATTR_CLASS] = LOAD_ATTR,
    [LOAD_ATTR_GETATTRIBUTE_OVERRIDDEN] = LOAD_ATTR,
    [LOAD_ATTR_INSTANCE_VALUE] = LOAD_ATTR,
    [LOAD_ATTR_METHOD_LAZY_DICT] = LOAD_ATTR,
    [LOAD_ATTR_METHOD_NO_DICT] = LOAD_ATTR,
    [LOAD_ATTR_METHOD_WITH_VALUES] = LOAD_ATTR,
    [LOAD_ATTR_MODULE] = LOAD_ATTR,
    [LOAD_ATTR_NONDESCRIPTOR_NO_DICT] = LOAD_ATTR,
    [LOAD_ATTR_NONDESCRIPTOR_WITH_VALUES] = LOAD_ATTR,
    [LOAD_ATTR_PROPERTY] = LOAD_ATTR,
    [LOAD_ATTR_SLOT] = LOAD_ATTR,
    [LOAD_ATTR_WITH_HINT] = LOAD_ATTR,
    [LOAD_BUILD_CLASS] = LOAD_BUILD_CLASS,
    [LOAD_CONST] = LOAD_CONST,
    [LOAD_DEREF] = LOAD_DEREF,
    [LOAD_FAST] = LOAD_FAST,
    [LOAD_FAST_AND_CLEAR] = LOAD_FAST_AND_CLEAR,
    [LOAD_FAST_CHECK] = LOAD_FAST_CHECK,
    [LOAD_FAST_LOAD_FAST] = LOAD_FAST_LOAD_FAST,
    [LOAD_FROM_DICT_OR_DEREF] = LOAD_FROM_DICT_OR_DEREF,
    [LOAD_FROM_DICT_OR_GLOBALS] = LOAD_FROM_DICT_OR_GLOBALS,
    [LOAD_GLOBAL] = LOAD_GLOBAL,
    [LOAD_GLOBAL_BUILTIN] = LOAD_GLOBAL,
    [LOAD_GLOBAL_MODULE] = LOAD_GLOBAL,
    [LOAD_LOCALS] = LOAD_LOCALS,
    [LOAD_NAME] = LOAD_NAME,
    [LOAD_SUPER_ATTR] = LOAD_SUPER_ATTR,
    [LOAD_SUPER_ATTR_ATTR] = LOAD_SUPER_ATTR,
    [LOAD_SUPER_ATTR_METHOD] = LOAD_SUPER_ATTR,
    [MAKE_CELL] = MAKE_CELL,
    [MAKE_FUNCTION] = MAKE_FUNCTION,
    [MAP_ADD] = MAP_ADD,
    [MATCH_CLASS] = MATCH_CLASS,
    [MATCH_KEYS] = MATCH_KEYS,
    [MATCH_MAPPING] = MATCH_MAPPING,
    [MATCH_SEQUENCE] = MATCH_SEQUENCE,
    [NOP] = NOP,
    [POP_EXCEPT] = POP_EXCEPT,
    [POP_JUMP_IF_FALSE] = POP_JUMP_IF_FALSE,
    [POP_JUMP_IF_NONE] = POP_JUMP_IF_NONE,
    [POP_JUMP_IF_NOT_NONE] = POP_JUMP_IF_NOT_NONE,
    [POP_JUMP_IF_TRUE] = POP_JUMP_IF_TRUE,
    [POP_TOP] = POP_TOP,
    [PUSH_EXC_INFO] = PUSH_EXC_INFO,
    [PUSH_NULL] = PUSH_NULL,
    [RAISE_VARARGS] = RAISE_VARARGS,
    [RERAISE] = RERAISE,
    [RESERVED] = RESERVED,
    [RESUME] = RESUME,
    [RESUME_CHECK] = RESUME,
    [RETURN_CONST] = RETURN_CONST,
    [RETURN_GENERATOR] = RETURN_GENERATOR,
    [RETURN_VALUE] = RETURN_VALUE,
    [SEND] = SEND,
    [SEND_GEN] = SEND,
    [SETUP_ANNOTATIONS] = SETUP_ANNOTATIONS,
    [SET_ADD] = SET_ADD,
    [SET_FUNCTION_ATTRIBUTE] = SET_FUNCTION_ATTRIBUTE,
    [SET_UPDATE] = SET_UPDATE,
    [STORE_ATTR] = STORE_ATTR,
    [STORE_ATTR_INSTANCE_VALUE] = STORE_ATTR,
    [STORE_ATTR_SLOT] = STORE_ATTR,
    [STORE_ATTR_WITH_HINT] = STORE_ATTR,
    [STORE_DEREF] = STORE_DEREF,
    [STORE_FAST] = STORE_FAST,
    [STORE_FAST_LOAD_FAST] = STORE_FAST_LOAD_FAST,
    [STORE_FAST_STORE_FAST] = STORE_FAST_STORE_FAST,
    [STORE_GLOBAL] = STORE_GLOBAL,
    [STORE_NAME] = STORE_NAME,
    [STORE_SLICE] = STORE_SLICE,
    [STORE_SUBSCR] = STORE_SUBSCR,
    [STORE_SUBSCR_DICT] = STORE_SUBSCR,
    [STORE_SUBSCR_LIST_INT] = STORE_SUBSCR,
    [SWAP] = SWAP,
    [TO_BOOL] = TO_BOOL,
    [TO_BOOL_ALWAYS_TRUE] = TO_BOOL,
    [TO_BOOL_BOOL] = TO_BOOL,
    [TO_BOOL_INT] = TO_BOOL,
    [TO_BOOL_LIST] = TO_BOOL,
    [TO_BOOL_NONE] = TO_BOOL,
    [TO_BOOL_STR] = TO_BOOL,
    [UNARY_INVERT] = UNARY_INVERT,
    [UNARY_NEGATIVE] = UNARY_NEGATIVE,
    [UNARY_NOT] = UNARY_NOT,
    [UNPACK_EX] = UNPACK_EX,
    [UNPACK_SEQUENCE] = UNPACK_SEQUENCE,
    [UNPACK_SEQUENCE_LIST] = UNPACK_SEQUENCE,
    [UNPACK_SEQUENCE_TUPLE] = UNPACK_SEQUENCE,
    [UNPACK_SEQUENCE_TWO_TUPLE] = UNPACK_SEQUENCE,
    [WITH_EXCEPT_START] = WITH_EXCEPT_START,
    [YIELD_VALUE] = YIELD_VALUE,
};

#endif // NEED_OPCODE_METADATA

#define EXTRA_CASES \
    case 119: \
    case 120: \
    case 121: \
    case 122: \
    case 123: \
    case 124: \
    case 125: \
    case 126: \
    case 127: \
    case 128: \
    case 129: \
    case 130: \
    case 131: \
    case 132: \
    case 133: \
    case 134: \
    case 135: \
    case 136: \
    case 137: \
    case 138: \
    case 139: \
    case 140: \
    case 141: \
    case 142: \
    case 143: \
    case 144: \
    case 145: \
    case 146: \
    case 147: \
    case 148: \
    case 223: \
    case 224: \
    case 225: \
    case 226: \
    case 227: \
    case 228: \
    case 229: \
    case 230: \
    case 231: \
    case 232: \
    case 233: \
    case 234: \
    case 235: \
    case 255: \
        ;
struct pseudo_targets {
    uint8_t targets[3];
};
extern const struct pseudo_targets _PyOpcode_PseudoTargets[12];
#ifdef NEED_OPCODE_METADATA
const struct pseudo_targets _PyOpcode_PseudoTargets[12] = {
    [LOAD_CLOSURE-256] = { { LOAD_FAST, 0, 0 } },
    [STORE_FAST_MAYBE_NULL-256] = { { STORE_FAST, 0, 0 } },
    [LOAD_SUPER_METHOD-256] = { { LOAD_SUPER_ATTR, 0, 0 } },
    [LOAD_ZERO_SUPER_METHOD-256] = { { LOAD_SUPER_ATTR, 0, 0 } },
    [LOAD_ZERO_SUPER_ATTR-256] = { { LOAD_SUPER_ATTR, 0, 0 } },
    [LOAD_METHOD-256] = { { LOAD_ATTR, 0, 0 } },
    [JUMP-256] = { { JUMP_FORWARD, JUMP_BACKWARD, 0 } },
    [JUMP_NO_INTERRUPT-256] = { { JUMP_FORWARD, JUMP_BACKWARD_NO_INTERRUPT, 0 } },
    [SETUP_FINALLY-256] = { { NOP, 0, 0 } },
    [SETUP_CLEANUP-256] = { { NOP, 0, 0 } },
    [SETUP_WITH-256] = { { NOP, 0, 0 } },
    [POP_BLOCK-256] = { { NOP, 0, 0 } },
};

#endif // NEED_OPCODE_METADATA
static inline bool
is_pseudo_target(int pseudo, int target) {
    if (pseudo < 256 || pseudo >= 268) {
        return false;
    }
    for (int i = 0; _PyOpcode_PseudoTargets[pseudo-256].targets[i]; i++) {
        if (_PyOpcode_PseudoTargets[pseudo-256].targets[i] == target) return true;
    }
    return false;
}


#ifdef __cplusplus
}
#endif
#endif /* !Py_CORE_OPCODE_METADATA_H */
