/*
 * tkIntDecls.h --
 *
 *	This file contains the declarations for all unsupported
 *	functions that are exported by the Tk library.  These
 *	interfaces are not guaranteed to remain the same between
 *	versions.  Use at your own risk.
 *
 * Copyright (c) 1998-1999 by Scriptics Corporation.
 *
 * See the file "license.terms" for information on usage and redistribution
 * of this file, and for a DISCLAIMER OF ALL WARRANTIES.
 */

#ifndef _TKINTDECLS
#define _TKINTDECLS

#ifdef BUILD_tk
#undef TCL_STORAGE_CLASS
#define TCL_STORAGE_CLASS DLLEXPORT
#endif

struct TkText;
typedef struct TkTextBTree_ *TkTextBTree;
struct TkTextDispChunk;
struct TkTextIndex;
struct TkTextSegment;
struct TkSharedText;

/*
 * WARNING: This file is automatically generated by the tools/genStubs.tcl
 * script.  Any modifications to the function declarations below should be made
 * in the generic/tkInt.decls script.
 */

/* !BEGIN!: Do not edit below this line. */

#ifdef __cplusplus
extern "C" {
#endif

/*
 * Exported function declarations:
 */

/* 0 */
EXTERN TkWindow *	TkAllocWindow(TkDisplay *dispPtr, int screenNum,
				TkWindow *parentPtr);
/* 1 */
EXTERN void		TkBezierPoints(double control[], int numSteps,
				double *coordPtr);
/* 2 */
EXTERN void		TkBezierScreenPoints(Tk_Canvas canvas,
				double control[], int numSteps,
				XPoint *xPointPtr);
/* Slot 3 is reserved */
/* 4 */
EXTERN void		TkBindEventProc(TkWindow *winPtr, XEvent *eventPtr);
/* 5 */
EXTERN void		TkBindFree(TkMainInfo *mainPtr);
/* 6 */
EXTERN void		TkBindInit(TkMainInfo *mainPtr);
/* 7 */
EXTERN void		TkChangeEventWindow(XEvent *eventPtr,
				TkWindow *winPtr);
/* 8 */
EXTERN int		TkClipInit(Tcl_Interp *interp, TkDisplay *dispPtr);
/* 9 */
EXTERN void		TkComputeAnchor(Tk_Anchor anchor, Tk_Window tkwin,
				int padX, int padY, int innerWidth,
				int innerHeight, int *xPtr, int *yPtr);
/* Slot 10 is reserved */
/* Slot 11 is reserved */
/* 12 */
EXTERN TkCursor *	TkCreateCursorFromData(Tk_Window tkwin,
				const char *source, const char *mask,
				int width, int height, int xHot, int yHot,
				XColor fg, XColor bg);
/* 13 */
EXTERN int		TkCreateFrame(ClientData clientData,
				Tcl_Interp *interp, int argc,
				const char *const *argv, int toplevel,
				const char *appName);
/* 14 */
EXTERN Tk_Window	TkCreateMainWindow(Tcl_Interp *interp,
				const char *screenName, const char *baseName);
/* 15 */
EXTERN Time		TkCurrentTime(TkDisplay *dispPtr);
/* 16 */
EXTERN void		TkDeleteAllImages(TkMainInfo *mainPtr);
/* 17 */
EXTERN void		TkDoConfigureNotify(TkWindow *winPtr);
/* 18 */
EXTERN void		TkDrawInsetFocusHighlight(Tk_Window tkwin, GC gc,
				int width, Drawable drawable, int padding);
/* 19 */
EXTERN void		TkEventDeadWindow(TkWindow *winPtr);
/* 20 */
EXTERN void		TkFillPolygon(Tk_Canvas canvas, double *coordPtr,
				int numPoints, Display *display,
				Drawable drawable, GC gc, GC outlineGC);
/* 21 */
EXTERN int		TkFindStateNum(Tcl_Interp *interp,
				const char *option, const TkStateMap *mapPtr,
				const char *strKey);
/* 22 */
EXTERN CONST86 char *	TkFindStateString(const TkStateMap *mapPtr,
				int numKey);
/* 23 */
EXTERN void		TkFocusDeadWindow(TkWindow *winPtr);
/* 24 */
EXTERN int		TkFocusFilterEvent(TkWindow *winPtr,
				XEvent *eventPtr);
/* 25 */
EXTERN TkWindow *	TkFocusKeyEvent(TkWindow *winPtr, XEvent *eventPtr);
/* 26 */
EXTERN void		TkFontPkgInit(TkMainInfo *mainPtr);
/* 27 */
EXTERN void		TkFontPkgFree(TkMainInfo *mainPtr);
/* 28 */
EXTERN void		TkFreeBindingTags(TkWindow *winPtr);
/* 29 */
EXTERN void		TkpFreeCursor(TkCursor *cursorPtr);
/* 30 */
EXTERN char *		TkGetBitmapData(Tcl_Interp *interp,
				const char *string, const char *fileName,
				int *widthPtr, int *heightPtr, int *hotXPtr,
				int *hotYPtr);
/* 31 */
EXTERN void		TkGetButtPoints(double p1[], double p2[],
				double width, int project, double m1[],
				double m2[]);
/* 32 */
EXTERN TkCursor *	TkGetCursorByName(Tcl_Interp *interp,
				Tk_Window tkwin, Tk_Uid string);
/* 33 */
EXTERN const char *	TkGetDefaultScreenName(Tcl_Interp *interp,
				const char *screenName);
/* 34 */
EXTERN TkDisplay *	TkGetDisplay(Display *display);
/* 35 */
EXTERN int		TkGetDisplayOf(Tcl_Interp *interp, int objc,
				Tcl_Obj *const objv[], Tk_Window *tkwinPtr);
/* 36 */
EXTERN TkWindow *	TkGetFocusWin(TkWindow *winPtr);
/* 37 */
EXTERN int		TkGetInterpNames(Tcl_Interp *interp, Tk_Window tkwin);
/* 38 */
EXTERN int		TkGetMiterPoints(double p1[], double p2[],
				double p3[], double width, double m1[],
				double m2[]);
/* 39 */
EXTERN void		TkGetPointerCoords(Tk_Window tkwin, int *xPtr,
				int *yPtr);
/* 40 */
EXTERN void		TkGetServerInfo(Tcl_Interp *interp, Tk_Window tkwin);
/* 41 */
EXTERN void		TkGrabDeadWindow(TkWindow *winPtr);
/* 42 */
EXTERN int		TkGrabState(TkWindow *winPtr);
/* 43 */
EXTERN void		TkIncludePoint(Tk_Item *itemPtr, double *pointPtr);
/* 44 */
EXTERN void		TkInOutEvents(XEvent *eventPtr, TkWindow *sourcePtr,
				TkWindow *destPtr, int leaveType,
				int enterType, Tcl_QueuePosition position);
/* 45 */
EXTERN void		TkInstallFrameMenu(Tk_Window tkwin);
/* 46 */
EXTERN CONST86 char *	TkKeysymToString(KeySym keysym);
/* 47 */
EXTERN int		TkLineToArea(double end1Ptr[], double end2Ptr[],
				double rectPtr[]);
/* 48 */
EXTERN double		TkLineToPoint(double end1Ptr[], double end2Ptr[],
				double pointPtr[]);
/* 49 */
EXTERN int		TkMakeBezierCurve(Tk_Canvas canvas, double *pointPtr,
				int numPoints, int numSteps,
				XPoint xPoints[], double dblPoints[]);
/* 50 */
EXTERN void		TkMakeBezierPostscript(Tcl_Interp *interp,
				Tk_Canvas canvas, double *pointPtr,
				int numPoints);
/* 51 */
EXTERN void		TkOptionClassChanged(TkWindow *winPtr);
/* 52 */
EXTERN void		TkOptionDeadWindow(TkWindow *winPtr);
/* 53 */
EXTERN int		TkOvalToArea(double *ovalPtr, double *rectPtr);
/* 54 */
EXTERN double		TkOvalToPoint(double ovalPtr[], double width,
				int filled, double pointPtr[]);
/* 55 */
EXTERN int		TkpChangeFocus(TkWindow *winPtr, int force);
/* 56 */
EXTERN void		TkpCloseDisplay(TkDisplay *dispPtr);
/* 57 */
EXTERN void		TkpClaimFocus(TkWindow *topLevelPtr, int force);
/* 58 */
EXTERN void		TkpDisplayWarning(const char *msg, const char *title);
/* 59 */
EXTERN void		TkpGetAppName(Tcl_Interp *interp, Tcl_DString *name);
/* 60 */
EXTERN TkWindow *	TkpGetOtherWindow(TkWindow *winPtr);
/* 61 */
EXTERN TkWindow *	TkpGetWrapperWindow(TkWindow *winPtr);
/* 62 */
EXTERN int		TkpInit(Tcl_Interp *interp);
/* 63 */
EXTERN void		TkpInitializeMenuBindings(Tcl_Interp *interp,
				Tk_BindingTable bindingTable);
/* 64 */
EXTERN void		TkpMakeContainer(Tk_Window tkwin);
/* 65 */
EXTERN void		TkpMakeMenuWindow(Tk_Window tkwin, int transient);
/* 66 */
EXTERN Window		TkpMakeWindow(TkWindow *winPtr, Window parent);
/* 67 */
EXTERN void		TkpMenuNotifyToplevelCreate(Tcl_Interp *interp,
				const char *menuName);
/* 68 */
EXTERN TkDisplay *	TkpOpenDisplay(const char *display_name);
/* 69 */
EXTERN int		TkPointerEvent(XEvent *eventPtr, TkWindow *winPtr);
/* 70 */
EXTERN int		TkPolygonToArea(double *polyPtr, int numPoints,
				double *rectPtr);
/* 71 */
EXTERN double		TkPolygonToPoint(double *polyPtr, int numPoints,
				double *pointPtr);
/* 72 */
EXTERN int		TkPositionInTree(TkWindow *winPtr, TkWindow *treePtr);
/* 73 */
EXTERN void		TkpRedirectKeyEvent(TkWindow *winPtr,
				XEvent *eventPtr);
/* 74 */
EXTERN void		TkpSetMainMenubar(Tcl_Interp *interp,
				Tk_Window tkwin, const char *menuName);
/* 75 */
EXTERN int		TkpUseWindow(Tcl_Interp *interp, Tk_Window tkwin,
				const char *string);
/* Slot 76 is reserved */
/* 77 */
EXTERN void		TkQueueEventForAllChildren(TkWindow *winPtr,
				XEvent *eventPtr);
/* 78 */
EXTERN int		TkReadBitmapFile(Display *display, Drawable d,
				const char *filename,
				unsigned int *width_return,
				unsigned int *height_return,
				Pixmap *bitmap_return, int *x_hot_return,
				int *y_hot_return);
/* 79 */
EXTERN int		TkScrollWindow(Tk_Window tkwin, GC gc, int x, int y,
				int width, int height, int dx, int dy,
				TkRegion damageRgn);
/* 80 */
EXTERN void		TkSelDeadWindow(TkWindow *winPtr);
/* 81 */
EXTERN void		TkSelEventProc(Tk_Window tkwin, XEvent *eventPtr);
/* 82 */
EXTERN void		TkSelInit(Tk_Window tkwin);
/* 83 */
EXTERN void		TkSelPropProc(XEvent *eventPtr);
/* Slot 84 is reserved */
/* 85 */
EXTERN void		TkSetWindowMenuBar(Tcl_Interp *interp,
				Tk_Window tkwin, const char *oldMenuName,
				const char *menuName);
/* 86 */
EXTERN KeySym		TkStringToKeysym(const char *name);
/* 87 */
EXTERN int		TkThickPolyLineToArea(double *coordPtr,
				int numPoints, double width, int capStyle,
				int joinStyle, double *rectPtr);
/* 88 */
EXTERN void		TkWmAddToColormapWindows(TkWindow *winPtr);
/* 89 */
EXTERN void		TkWmDeadWindow(TkWindow *winPtr);
/* 90 */
EXTERN TkWindow *	TkWmFocusToplevel(TkWindow *winPtr);
/* 91 */
EXTERN void		TkWmMapWindow(TkWindow *winPtr);
/* 92 */
EXTERN void		TkWmNewWindow(TkWindow *winPtr);
/* 93 */
EXTERN void		TkWmProtocolEventProc(TkWindow *winPtr,
				XEvent *evenvPtr);
/* 94 */
EXTERN void		TkWmRemoveFromColormapWindows(TkWindow *winPtr);
/* 95 */
EXTERN void		TkWmRestackToplevel(TkWindow *winPtr, int aboveBelow,
				TkWindow *otherPtr);
/* 96 */
EXTERN void		TkWmSetClass(TkWindow *winPtr);
/* 97 */
EXTERN void		TkWmUnmapWindow(TkWindow *winPtr);
/* 98 */
EXTERN Tcl_Obj *	TkDebugBitmap(Tk_Window tkwin, const char *name);
/* 99 */
EXTERN Tcl_Obj *	TkDebugBorder(Tk_Window tkwin, const char *name);
/* 100 */
EXTERN Tcl_Obj *	TkDebugCursor(Tk_Window tkwin, const char *name);
/* 101 */
EXTERN Tcl_Obj *	TkDebugColor(Tk_Window tkwin, const char *name);
/* 102 */
EXTERN Tcl_Obj *	TkDebugConfig(Tcl_Interp *interp,
				Tk_OptionTable table);
/* 103 */
EXTERN Tcl_Obj *	TkDebugFont(Tk_Window tkwin, const char *name);
/* 104 */
EXTERN int		TkFindStateNumObj(Tcl_Interp *interp,
				Tcl_Obj *optionPtr, const TkStateMap *mapPtr,
				Tcl_Obj *keyPtr);
/* 105 */
EXTERN Tcl_HashTable *	TkGetBitmapPredefTable(void);
/* 106 */
EXTERN TkDisplay *	TkGetDisplayList(void);
/* 107 */
EXTERN TkMainInfo *	TkGetMainInfoList(void);
/* 108 */
EXTERN int		TkGetWindowFromObj(Tcl_Interp *interp,
				Tk_Window tkwin, Tcl_Obj *objPtr,
				Tk_Window *windowPtr);
/* 109 */
EXTERN CONST86 char *	TkpGetString(TkWindow *winPtr, XEvent *eventPtr,
				Tcl_DString *dsPtr);
/* 110 */
EXTERN void		TkpGetSubFonts(Tcl_Interp *interp, Tk_Font tkfont);
/* 111 */
EXTERN Tcl_Obj *	TkpGetSystemDefault(Tk_Window tkwin,
				const char *dbName, const char *className);
/* 112 */
EXTERN void		TkpMenuThreadInit(void);
/* 113 */
EXTERN int		TkClipBox(TkRegion rgn, XRectangle *rect_return);
/* 114 */
EXTERN TkRegion		TkCreateRegion(void);
/* 115 */
EXTERN int		TkDestroyRegion(TkRegion rgn);
/* 116 */
EXTERN int		TkIntersectRegion(TkRegion sra, TkRegion srcb,
				TkRegion dr_return);
/* 117 */
EXTERN int		TkRectInRegion(TkRegion rgn, int x, int y,
				unsigned int width, unsigned int height);
/* 118 */
EXTERN int		TkSetRegion(Display *display, GC gc, TkRegion rgn);
/* 119 */
EXTERN int		TkUnionRectWithRegion(XRectangle *rect, TkRegion src,
				TkRegion dr_return);
/* Slot 120 is reserved */
#ifdef MAC_OSX_TK /* AQUA */
/* 121 */
EXTERN Pixmap		TkpCreateNativeBitmap(Display *display,
				const void *source);
#endif /* AQUA */
#ifdef MAC_OSX_TK /* AQUA */
/* 122 */
EXTERN void		TkpDefineNativeBitmaps(void);
#endif /* AQUA */
/* Slot 123 is reserved */
#ifdef MAC_OSX_TK /* AQUA */
/* 124 */
EXTERN Pixmap		TkpGetNativeAppBitmap(Display *display,
				const char *name, int *width, int *height);
#endif /* AQUA */
/* Slot 125 is reserved */
/* Slot 126 is reserved */
/* Slot 127 is reserved */
/* Slot 128 is reserved */
/* Slot 129 is reserved */
/* Slot 130 is reserved */
/* Slot 131 is reserved */
/* Slot 132 is reserved */
/* Slot 133 is reserved */
/* Slot 134 is reserved */
/* 135 */
EXTERN void		TkpDrawHighlightBorder(Tk_Window tkwin, GC fgGC,
				GC bgGC, int highlightWidth,
				Drawable drawable);
/* 136 */
EXTERN void		TkSetFocusWin(TkWindow *winPtr, int force);
/* 137 */
EXTERN void		TkpSetKeycodeAndState(Tk_Window tkwin, KeySym keySym,
				XEvent *eventPtr);
/* 138 */
EXTERN KeySym		TkpGetKeySym(TkDisplay *dispPtr, XEvent *eventPtr);
/* 139 */
EXTERN void		TkpInitKeymapInfo(TkDisplay *dispPtr);
/* 140 */
EXTERN TkRegion		TkPhotoGetValidRegion(Tk_PhotoHandle handle);
/* 141 */
EXTERN TkWindow **	TkWmStackorderToplevel(TkWindow *parentPtr);
/* 142 */
EXTERN void		TkFocusFree(TkMainInfo *mainPtr);
/* 143 */
EXTERN void		TkClipCleanup(TkDisplay *dispPtr);
/* 144 */
EXTERN void		TkGCCleanup(TkDisplay *dispPtr);
/* 145 */
EXTERN int		TkSubtractRegion(TkRegion sra, TkRegion srcb,
				TkRegion dr_return);
/* 146 */
EXTERN void		TkStylePkgInit(TkMainInfo *mainPtr);
/* 147 */
EXTERN void		TkStylePkgFree(TkMainInfo *mainPtr);
/* 148 */
EXTERN Tk_Window	TkToplevelWindowForCommand(Tcl_Interp *interp,
				const char *cmdName);
/* 149 */
EXTERN const Tk_OptionSpec * TkGetOptionSpec(const char *name,
				Tk_OptionTable optionTable);
/* 150 */
EXTERN int		TkMakeRawCurve(Tk_Canvas canvas, double *pointPtr,
				int numPoints, int numSteps,
				XPoint xPoints[], double dblPoints[]);
/* 151 */
EXTERN void		TkMakeRawCurvePostscript(Tcl_Interp *interp,
				Tk_Canvas canvas, double *pointPtr,
				int numPoints);
/* 152 */
EXTERN void		TkpDrawFrame(Tk_Window tkwin, Tk_3DBorder border,
				int highlightWidth, int borderWidth,
				int relief);
/* 153 */
EXTERN void		TkCreateThreadExitHandler(Tcl_ExitProc *proc,
				ClientData clientData);
/* 154 */
EXTERN void		TkDeleteThreadExitHandler(Tcl_ExitProc *proc,
				ClientData clientData);
/* Slot 155 is reserved */
/* 156 */
EXTERN int		TkpTestembedCmd(ClientData clientData,
				Tcl_Interp *interp, int objc,
				Tcl_Obj *const objv[]);
/* 157 */
EXTERN int		TkpTesttextCmd(ClientData dummy, Tcl_Interp *interp,
				int objc, Tcl_Obj *const objv[]);
/* 158 */
EXTERN int		TkSelGetSelection(Tcl_Interp *interp,
				Tk_Window tkwin, Atom selection, Atom target,
				Tk_GetSelProc *proc, ClientData clientData);
/* 159 */
EXTERN int		TkTextGetIndex(Tcl_Interp *interp,
				struct TkText *textPtr, const char *string,
				struct TkTextIndex *indexPtr);
/* 160 */
EXTERN int		TkTextIndexBackBytes(const struct TkText *textPtr,
				const struct TkTextIndex *srcPtr, int count,
				struct TkTextIndex *dstPtr);
/* 161 */
EXTERN int		TkTextIndexForwBytes(const struct TkText *textPtr,
				const struct TkTextIndex *srcPtr, int count,
				struct TkTextIndex *dstPtr);
/* 162 */
EXTERN struct TkTextIndex * TkTextMakeByteIndex(TkTextBTree tree,
				const struct TkText *textPtr, int lineIndex,
				int byteIndex, struct TkTextIndex *indexPtr);
/* 163 */
EXTERN int		TkTextPrintIndex(const struct TkText *textPtr,
				const struct TkTextIndex *indexPtr,
				char *string);
/* 164 */
EXTERN struct TkTextSegment * TkTextSetMark(struct TkText *textPtr,
				const char *name,
				struct TkTextIndex *indexPtr);
/* 165 */
EXTERN int		TkTextXviewCmd(struct TkText *textPtr,
				Tcl_Interp *interp, int objc,
				Tcl_Obj *const objv[]);
/* 166 */
EXTERN void		TkTextChanged(struct TkSharedText *sharedTextPtr,
				struct TkText *textPtr,
				const struct TkTextIndex *index1Ptr,
				const struct TkTextIndex *index2Ptr);
/* 167 */
EXTERN int		TkBTreeNumLines(TkTextBTree tree,
				const struct TkText *textPtr);
/* 168 */
EXTERN void		TkTextInsertDisplayProc(struct TkText *textPtr,
				struct TkTextDispChunk *chunkPtr, int x,
				int y, int height, int baseline,
				Display *display, Drawable dst, int screenY);
/* 169 */
EXTERN int		TkStateParseProc(ClientData clientData,
				Tcl_Interp *interp, Tk_Window tkwin,
				const char *value, char *widgRec, int offset);
/* 170 */
EXTERN CONST86 char *	TkStatePrintProc(ClientData clientData,
				Tk_Window tkwin, char *widgRec, int offset,
				Tcl_FreeProc **freeProcPtr);
/* 171 */
EXTERN int		TkCanvasDashParseProc(ClientData clientData,
				Tcl_Interp *interp, Tk_Window tkwin,
				const char *value, char *widgRec, int offset);
/* 172 */
EXTERN CONST86 char *	TkCanvasDashPrintProc(ClientData clientData,
				Tk_Window tkwin, char *widgRec, int offset,
				Tcl_FreeProc **freeProcPtr);
/* 173 */
EXTERN int		TkOffsetParseProc(ClientData clientData,
				Tcl_Interp *interp, Tk_Window tkwin,
				const char *value, char *widgRec, int offset);
/* 174 */
EXTERN CONST86 char *	TkOffsetPrintProc(ClientData clientData,
				Tk_Window tkwin, char *widgRec, int offset,
				Tcl_FreeProc **freeProcPtr);
/* 175 */
EXTERN int		TkPixelParseProc(ClientData clientData,
				Tcl_Interp *interp, Tk_Window tkwin,
				const char *value, char *widgRec, int offset);
/* 176 */
EXTERN CONST86 char *	TkPixelPrintProc(ClientData clientData,
				Tk_Window tkwin, char *widgRec, int offset,
				Tcl_FreeProc **freeProcPtr);
/* 177 */
EXTERN int		TkOrientParseProc(ClientData clientData,
				Tcl_Interp *interp, Tk_Window tkwin,
				const char *value, char *widgRec, int offset);
/* 178 */
EXTERN CONST86 char *	TkOrientPrintProc(ClientData clientData,
				Tk_Window tkwin, char *widgRec, int offset,
				Tcl_FreeProc **freeProcPtr);
/* 179 */
EXTERN int		TkSmoothParseProc(ClientData clientData,
				Tcl_Interp *interp, Tk_Window tkwin,
				const char *value, char *widgRec, int offset);
/* 180 */
EXTERN CONST86 char *	TkSmoothPrintProc(ClientData clientData,
				Tk_Window tkwin, char *widgRec, int offset,
				Tcl_FreeProc **freeProcPtr);
/* 181 */
EXTERN void		TkDrawAngledTextLayout(Display *display,
				Drawable drawable, GC gc,
				Tk_TextLayout layout, int x, int y,
				double angle, int firstChar, int lastChar);
/* 182 */
EXTERN void		TkUnderlineAngledTextLayout(Display *display,
				Drawable drawable, GC gc,
				Tk_TextLayout layout, int x, int y,
				double angle, int underline);
/* 183 */
EXTERN int		TkIntersectAngledTextLayout(Tk_TextLayout layout,
				int x, int y, int width, int height,
				double angle);
/* 184 */
EXTERN void		TkDrawAngledChars(Display *display,
				Drawable drawable, GC gc, Tk_Font tkfont,
				const char *source, int numBytes, double x,
				double y, double angle);
#ifdef MAC_OSX_TCL /* MACOSX */
/* 185 */
EXTERN void		TkpRedrawWidget(Tk_Window tkwin);
#endif /* MACOSX */
#ifdef MAC_OSX_TCL /* MACOSX */
/* 186 */
EXTERN int		TkpWillDrawWidget(Tk_Window tkwin);
#endif /* MACOSX */
/* 187 */
EXTERN void		TkUnusedStubEntry(void);

typedef struct TkIntStubs {
    int magic;
    void *hooks;

    TkWindow * (*tkAllocWindow) (TkDisplay *dispPtr, int screenNum, TkWindow *parentPtr); /* 0 */
    void (*tkBezierPoints) (double control[], int numSteps, double *coordPtr); /* 1 */
    void (*tkBezierScreenPoints) (Tk_Canvas canvas, double control[], int numSteps, XPoint *xPointPtr); /* 2 */
    void (*reserved3)(void);
    void (*tkBindEventProc) (TkWindow *winPtr, XEvent *eventPtr); /* 4 */
    void (*tkBindFree) (TkMainInfo *mainPtr); /* 5 */
    void (*tkBindInit) (TkMainInfo *mainPtr); /* 6 */
    void (*tkChangeEventWindow) (XEvent *eventPtr, TkWindow *winPtr); /* 7 */
    int (*tkClipInit) (Tcl_Interp *interp, TkDisplay *dispPtr); /* 8 */
    void (*tkComputeAnchor) (Tk_Anchor anchor, Tk_Window tkwin, int padX, int padY, int innerWidth, int innerHeight, int *xPtr, int *yPtr); /* 9 */
    void (*reserved10)(void);
    void (*reserved11)(void);
    TkCursor * (*tkCreateCursorFromData) (Tk_Window tkwin, const char *source, const char *mask, int width, int height, int xHot, int yHot, XColor fg, XColor bg); /* 12 */
    int (*tkCreateFrame) (ClientData clientData, Tcl_Interp *interp, int argc, const char *const *argv, int toplevel, const char *appName); /* 13 */
    Tk_Window (*tkCreateMainWindow) (Tcl_Interp *interp, const char *screenName, const char *baseName); /* 14 */
    Time (*tkCurrentTime) (TkDisplay *dispPtr); /* 15 */
    void (*tkDeleteAllImages) (TkMainInfo *mainPtr); /* 16 */
    void (*tkDoConfigureNotify) (TkWindow *winPtr); /* 17 */
    void (*tkDrawInsetFocusHighlight) (Tk_Window tkwin, GC gc, int width, Drawable drawable, int padding); /* 18 */
    void (*tkEventDeadWindow) (TkWindow *winPtr); /* 19 */
    void (*tkFillPolygon) (Tk_Canvas canvas, double *coordPtr, int numPoints, Display *display, Drawable drawable, GC gc, GC outlineGC); /* 20 */
    int (*tkFindStateNum) (Tcl_Interp *interp, const char *option, const TkStateMap *mapPtr, const char *strKey); /* 21 */
    CONST86 char * (*tkFindStateString) (const TkStateMap *mapPtr, int numKey); /* 22 */
    void (*tkFocusDeadWindow) (TkWindow *winPtr); /* 23 */
    int (*tkFocusFilterEvent) (TkWindow *winPtr, XEvent *eventPtr); /* 24 */
    TkWindow * (*tkFocusKeyEvent) (TkWindow *winPtr, XEvent *eventPtr); /* 25 */
    void (*tkFontPkgInit) (TkMainInfo *mainPtr); /* 26 */
    void (*tkFontPkgFree) (TkMainInfo *mainPtr); /* 27 */
    void (*tkFreeBindingTags) (TkWindow *winPtr); /* 28 */
    void (*tkpFreeCursor) (TkCursor *cursorPtr); /* 29 */
    char * (*tkGetBitmapData) (Tcl_Interp *interp, const char *string, const char *fileName, int *widthPtr, int *heightPtr, int *hotXPtr, int *hotYPtr); /* 30 */
    void (*tkGetButtPoints) (double p1[], double p2[], double width, int project, double m1[], double m2[]); /* 31 */
    TkCursor * (*tkGetCursorByName) (Tcl_Interp *interp, Tk_Window tkwin, Tk_Uid string); /* 32 */
    const char * (*tkGetDefaultScreenName) (Tcl_Interp *interp, const char *screenName); /* 33 */
    TkDisplay * (*tkGetDisplay) (Display *display); /* 34 */
    int (*tkGetDisplayOf) (Tcl_Interp *interp, int objc, Tcl_Obj *const objv[], Tk_Window *tkwinPtr); /* 35 */
    TkWindow * (*tkGetFocusWin) (TkWindow *winPtr); /* 36 */
    int (*tkGetInterpNames) (Tcl_Interp *interp, Tk_Window tkwin); /* 37 */
    int (*tkGetMiterPoints) (double p1[], double p2[], double p3[], double width, double m1[], double m2[]); /* 38 */
    void (*tkGetPointerCoords) (Tk_Window tkwin, int *xPtr, int *yPtr); /* 39 */
    void (*tkGetServerInfo) (Tcl_Interp *interp, Tk_Window tkwin); /* 40 */
    void (*tkGrabDeadWindow) (TkWindow *winPtr); /* 41 */
    int (*tkGrabState) (TkWindow *winPtr); /* 42 */
    void (*tkIncludePoint) (Tk_Item *itemPtr, double *pointPtr); /* 43 */
    void (*tkInOutEvents) (XEvent *eventPtr, TkWindow *sourcePtr, TkWindow *destPtr, int leaveType, int enterType, Tcl_QueuePosition position); /* 44 */
    void (*tkInstallFrameMenu) (Tk_Window tkwin); /* 45 */
    CONST86 char * (*tkKeysymToString) (KeySym keysym); /* 46 */
    int (*tkLineToArea) (double end1Ptr[], double end2Ptr[], double rectPtr[]); /* 47 */
    double (*tkLineToPoint) (double end1Ptr[], double end2Ptr[], double pointPtr[]); /* 48 */
    int (*tkMakeBezierCurve) (Tk_Canvas canvas, double *pointPtr, int numPoints, int numSteps, XPoint xPoints[], double dblPoints[]); /* 49 */
    void (*tkMakeBezierPostscript) (Tcl_Interp *interp, Tk_Canvas canvas, double *pointPtr, int numPoints); /* 50 */
    void (*tkOptionClassChanged) (TkWindow *winPtr); /* 51 */
    void (*tkOptionDeadWindow) (TkWindow *winPtr); /* 52 */
    int (*tkOvalToArea) (double *ovalPtr, double *rectPtr); /* 53 */
    double (*tkOvalToPoint) (double ovalPtr[], double width, int filled, double pointPtr[]); /* 54 */
    int (*tkpChangeFocus) (TkWindow *winPtr, int force); /* 55 */
    void (*tkpCloseDisplay) (TkDisplay *dispPtr); /* 56 */
    void (*tkpClaimFocus) (TkWindow *topLevelPtr, int force); /* 57 */
    void (*tkpDisplayWarning) (const char *msg, const char *title); /* 58 */
    void (*tkpGetAppName) (Tcl_Interp *interp, Tcl_DString *name); /* 59 */
    TkWindow * (*tkpGetOtherWindow) (TkWindow *winPtr); /* 60 */
    TkWindow * (*tkpGetWrapperWindow) (TkWindow *winPtr); /* 61 */
    int (*tkpInit) (Tcl_Interp *interp); /* 62 */
    void (*tkpInitializeMenuBindings) (Tcl_Interp *interp, Tk_BindingTable bindingTable); /* 63 */
    void (*tkpMakeContainer) (Tk_Window tkwin); /* 64 */
    void (*tkpMakeMenuWindow) (Tk_Window tkwin, int transient); /* 65 */
    Window (*tkpMakeWindow) (TkWindow *winPtr, Window parent); /* 66 */
    void (*tkpMenuNotifyToplevelCreate) (Tcl_Interp *interp, const char *menuName); /* 67 */
    TkDisplay * (*tkpOpenDisplay) (const char *display_name); /* 68 */
    int (*tkPointerEvent) (XEvent *eventPtr, TkWindow *winPtr); /* 69 */
    int (*tkPolygonToArea) (double *polyPtr, int numPoints, double *rectPtr); /* 70 */
    double (*tkPolygonToPoint) (double *polyPtr, int numPoints, double *pointPtr); /* 71 */
    int (*tkPositionInTree) (TkWindow *winPtr, TkWindow *treePtr); /* 72 */
    void (*tkpRedirectKeyEvent) (TkWindow *winPtr, XEvent *eventPtr); /* 73 */
    void (*tkpSetMainMenubar) (Tcl_Interp *interp, Tk_Window tkwin, const char *menuName); /* 74 */
    int (*tkpUseWindow) (Tcl_Interp *interp, Tk_Window tkwin, const char *string); /* 75 */
    void (*reserved76)(void);
    void (*tkQueueEventForAllChildren) (TkWindow *winPtr, XEvent *eventPtr); /* 77 */
    int (*tkReadBitmapFile) (Display *display, Drawable d, const char *filename, unsigned int *width_return, unsigned int *height_return, Pixmap *bitmap_return, int *x_hot_return, int *y_hot_return); /* 78 */
    int (*tkScrollWindow) (Tk_Window tkwin, GC gc, int x, int y, int width, int height, int dx, int dy, TkRegion damageRgn); /* 79 */
    void (*tkSelDeadWindow) (TkWindow *winPtr); /* 80 */
    void (*tkSelEventProc) (Tk_Window tkwin, XEvent *eventPtr); /* 81 */
    void (*tkSelInit) (Tk_Window tkwin); /* 82 */
    void (*tkSelPropProc) (XEvent *eventPtr); /* 83 */
    void (*reserved84)(void);
    void (*tkSetWindowMenuBar) (Tcl_Interp *interp, Tk_Window tkwin, const char *oldMenuName, const char *menuName); /* 85 */
    KeySym (*tkStringToKeysym) (const char *name); /* 86 */
    int (*tkThickPolyLineToArea) (double *coordPtr, int numPoints, double width, int capStyle, int joinStyle, double *rectPtr); /* 87 */
    void (*tkWmAddToColormapWindows) (TkWindow *winPtr); /* 88 */
    void (*tkWmDeadWindow) (TkWindow *winPtr); /* 89 */
    TkWindow * (*tkWmFocusToplevel) (TkWindow *winPtr); /* 90 */
    void (*tkWmMapWindow) (TkWindow *winPtr); /* 91 */
    void (*tkWmNewWindow) (TkWindow *winPtr); /* 92 */
    void (*tkWmProtocolEventProc) (TkWindow *winPtr, XEvent *evenvPtr); /* 93 */
    void (*tkWmRemoveFromColormapWindows) (TkWindow *winPtr); /* 94 */
    void (*tkWmRestackToplevel) (TkWindow *winPtr, int aboveBelow, TkWindow *otherPtr); /* 95 */
    void (*tkWmSetClass) (TkWindow *winPtr); /* 96 */
    void (*tkWmUnmapWindow) (TkWindow *winPtr); /* 97 */
    Tcl_Obj * (*tkDebugBitmap) (Tk_Window tkwin, const char *name); /* 98 */
    Tcl_Obj * (*tkDebugBorder) (Tk_Window tkwin, const char *name); /* 99 */
    Tcl_Obj * (*tkDebugCursor) (Tk_Window tkwin, const char *name); /* 100 */
    Tcl_Obj * (*tkDebugColor) (Tk_Window tkwin, const char *name); /* 101 */
    Tcl_Obj * (*tkDebugConfig) (Tcl_Interp *interp, Tk_OptionTable table); /* 102 */
    Tcl_Obj * (*tkDebugFont) (Tk_Window tkwin, const char *name); /* 103 */
    int (*tkFindStateNumObj) (Tcl_Interp *interp, Tcl_Obj *optionPtr, const TkStateMap *mapPtr, Tcl_Obj *keyPtr); /* 104 */
    Tcl_HashTable * (*tkGetBitmapPredefTable) (void); /* 105 */
    TkDisplay * (*tkGetDisplayList) (void); /* 106 */
    TkMainInfo * (*tkGetMainInfoList) (void); /* 107 */
    int (*tkGetWindowFromObj) (Tcl_Interp *interp, Tk_Window tkwin, Tcl_Obj *objPtr, Tk_Window *windowPtr); /* 108 */
    CONST86 char * (*tkpGetString) (TkWindow *winPtr, XEvent *eventPtr, Tcl_DString *dsPtr); /* 109 */
    void (*tkpGetSubFonts) (Tcl_Interp *interp, Tk_Font tkfont); /* 110 */
    Tcl_Obj * (*tkpGetSystemDefault) (Tk_Window tkwin, const char *dbName, const char *className); /* 111 */
    void (*tkpMenuThreadInit) (void); /* 112 */
    int (*tkClipBox) (TkRegion rgn, XRectangle *rect_return); /* 113 */
    TkRegion (*tkCreateRegion) (void); /* 114 */
    int (*tkDestroyRegion) (TkRegion rgn); /* 115 */
    int (*tkIntersectRegion) (TkRegion sra, TkRegion srcb, TkRegion dr_return); /* 116 */
    int (*tkRectInRegion) (TkRegion rgn, int x, int y, unsigned int width, unsigned int height); /* 117 */
    int (*tkSetRegion) (Display *display, GC gc, TkRegion rgn); /* 118 */
    int (*tkUnionRectWithRegion) (XRectangle *rect, TkRegion src, TkRegion dr_return); /* 119 */
    void (*reserved120)(void);
#if !(defined(_WIN32) || defined(MAC_OSX_TK)) /* X11 */
    void (*reserved121)(void);
#endif /* X11 */
#if defined(_WIN32) /* WIN */
    void (*reserved121)(void);
#endif /* WIN */
#ifdef MAC_OSX_TK /* AQUA */
    void (*reserved121)(void); /* Dummy entry for stubs table backwards compatibility */
    Pixmap (*tkpCreateNativeBitmap) (Display *display, const void *source); /* 121 */
#endif /* AQUA */
#if !(defined(_WIN32) || defined(MAC_OSX_TK)) /* X11 */
    void (*reserved122)(void);
#endif /* X11 */
#if defined(_WIN32) /* WIN */
    void (*reserved122)(void);
#endif /* WIN */
#ifdef MAC_OSX_TK /* AQUA */
    void (*reserved122)(void); /* Dummy entry for stubs table backwards compatibility */
    void (*tkpDefineNativeBitmaps) (void); /* 122 */
#endif /* AQUA */
    void (*reserved123)(void);
#if !(defined(_WIN32) || defined(MAC_OSX_TK)) /* X11 */
    void (*reserved124)(void);
#endif /* X11 */
#if defined(_WIN32) /* WIN */
    void (*reserved124)(void);
#endif /* WIN */
#ifdef MAC_OSX_TK /* AQUA */
    void (*reserved124)(void); /* Dummy entry for stubs table backwards compatibility */
    Pixmap (*tkpGetNativeAppBitmap) (Display *display, const char *name, int *width, int *height); /* 124 */
#endif /* AQUA */
    void (*reserved125)(void);
    void (*reserved126)(void);
    void (*reserved127)(void);
    void (*reserved128)(void);
    void (*reserved129)(void);
    void (*reserved130)(void);
    void (*reserved131)(void);
    void (*reserved132)(void);
    void (*reserved133)(void);
    void (*reserved134)(void);
    void (*tkpDrawHighlightBorder) (Tk_Window tkwin, GC fgGC, GC bgGC, int highlightWidth, Drawable drawable); /* 135 */
    void (*tkSetFocusWin) (TkWindow *winPtr, int force); /* 136 */
    void (*tkpSetKeycodeAndState) (Tk_Window tkwin, KeySym keySym, XEvent *eventPtr); /* 137 */
    KeySym (*tkpGetKeySym) (TkDisplay *dispPtr, XEvent *eventPtr); /* 138 */
    void (*tkpInitKeymapInfo) (TkDisplay *dispPtr); /* 139 */
    TkRegion (*tkPhotoGetValidRegion) (Tk_PhotoHandle handle); /* 140 */
    TkWindow ** (*tkWmStackorderToplevel) (TkWindow *parentPtr); /* 141 */
    void (*tkFocusFree) (TkMainInfo *mainPtr); /* 142 */
    void (*tkClipCleanup) (TkDisplay *dispPtr); /* 143 */
    void (*tkGCCleanup) (TkDisplay *dispPtr); /* 144 */
    int (*tkSubtractRegion) (TkRegion sra, TkRegion srcb, TkRegion dr_return); /* 145 */
    void (*tkStylePkgInit) (TkMainInfo *mainPtr); /* 146 */
    void (*tkStylePkgFree) (TkMainInfo *mainPtr); /* 147 */
    Tk_Window (*tkToplevelWindowForCommand) (Tcl_Interp *interp, const char *cmdName); /* 148 */
    const Tk_OptionSpec * (*tkGetOptionSpec) (const char *name, Tk_OptionTable optionTable); /* 149 */
    int (*tkMakeRawCurve) (Tk_Canvas canvas, double *pointPtr, int numPoints, int numSteps, XPoint xPoints[], double dblPoints[]); /* 150 */
    void (*tkMakeRawCurvePostscript) (Tcl_Interp *interp, Tk_Canvas canvas, double *pointPtr, int numPoints); /* 151 */
    void (*tkpDrawFrame) (Tk_Window tkwin, Tk_3DBorder border, int highlightWidth, int borderWidth, int relief); /* 152 */
    void (*tkCreateThreadExitHandler) (Tcl_ExitProc *proc, ClientData clientData); /* 153 */
    void (*tkDeleteThreadExitHandler) (Tcl_ExitProc *proc, ClientData clientData); /* 154 */
    void (*reserved155)(void);
    int (*tkpTestembedCmd) (ClientData clientData, Tcl_Interp *interp, int objc, Tcl_Obj *const objv[]); /* 156 */
    int (*tkpTesttextCmd) (ClientData dummy, Tcl_Interp *interp, int objc, Tcl_Obj *const objv[]); /* 157 */
    int (*tkSelGetSelection) (Tcl_Interp *interp, Tk_Window tkwin, Atom selection, Atom target, Tk_GetSelProc *proc, ClientData clientData); /* 158 */
    int (*tkTextGetIndex) (Tcl_Interp *interp, struct TkText *textPtr, const char *string, struct TkTextIndex *indexPtr); /* 159 */
    int (*tkTextIndexBackBytes) (const struct TkText *textPtr, const struct TkTextIndex *srcPtr, int count, struct TkTextIndex *dstPtr); /* 160 */
    int (*tkTextIndexForwBytes) (const struct TkText *textPtr, const struct TkTextIndex *srcPtr, int count, struct TkTextIndex *dstPtr); /* 161 */
    struct TkTextIndex * (*tkTextMakeByteIndex) (TkTextBTree tree, const struct TkText *textPtr, int lineIndex, int byteIndex, struct TkTextIndex *indexPtr); /* 162 */
    int (*tkTextPrintIndex) (const struct TkText *textPtr, const struct TkTextIndex *indexPtr, char *string); /* 163 */
    struct TkTextSegment * (*tkTextSetMark) (struct TkText *textPtr, const char *name, struct TkTextIndex *indexPtr); /* 164 */
    int (*tkTextXviewCmd) (struct TkText *textPtr, Tcl_Interp *interp, int objc, Tcl_Obj *const objv[]); /* 165 */
    void (*tkTextChanged) (struct TkSharedText *sharedTextPtr, struct TkText *textPtr, const struct TkTextIndex *index1Ptr, const struct TkTextIndex *index2Ptr); /* 166 */
    int (*tkBTreeNumLines) (TkTextBTree tree, const struct TkText *textPtr); /* 167 */
    void (*tkTextInsertDisplayProc) (struct TkText *textPtr, struct TkTextDispChunk *chunkPtr, int x, int y, int height, int baseline, Display *display, Drawable dst, int screenY); /* 168 */
    int (*tkStateParseProc) (ClientData clientData, Tcl_Interp *interp, Tk_Window tkwin, const char *value, char *widgRec, int offset); /* 169 */
    CONST86 char * (*tkStatePrintProc) (ClientData clientData, Tk_Window tkwin, char *widgRec, int offset, Tcl_FreeProc **freeProcPtr); /* 170 */
    int (*tkCanvasDashParseProc) (ClientData clientData, Tcl_Interp *interp, Tk_Window tkwin, const char *value, char *widgRec, int offset); /* 171 */
    CONST86 char * (*tkCanvasDashPrintProc) (ClientData clientData, Tk_Window tkwin, char *widgRec, int offset, Tcl_FreeProc **freeProcPtr); /* 172 */
    int (*tkOffsetParseProc) (ClientData clientData, Tcl_Interp *interp, Tk_Window tkwin, const char *value, char *widgRec, int offset); /* 173 */
    CONST86 char * (*tkOffsetPrintProc) (ClientData clientData, Tk_Window tkwin, char *widgRec, int offset, Tcl_FreeProc **freeProcPtr); /* 174 */
    int (*tkPixelParseProc) (ClientData clientData, Tcl_Interp *interp, Tk_Window tkwin, const char *value, char *widgRec, int offset); /* 175 */
    CONST86 char * (*tkPixelPrintProc) (ClientData clientData, Tk_Window tkwin, char *widgRec, int offset, Tcl_FreeProc **freeProcPtr); /* 176 */
    int (*tkOrientParseProc) (ClientData clientData, Tcl_Interp *interp, Tk_Window tkwin, const char *value, char *widgRec, int offset); /* 177 */
    CONST86 char * (*tkOrientPrintProc) (ClientData clientData, Tk_Window tkwin, char *widgRec, int offset, Tcl_FreeProc **freeProcPtr); /* 178 */
    int (*tkSmoothParseProc) (ClientData clientData, Tcl_Interp *interp, Tk_Window tkwin, const char *value, char *widgRec, int offset); /* 179 */
    CONST86 char * (*tkSmoothPrintProc) (ClientData clientData, Tk_Window tkwin, char *widgRec, int offset, Tcl_FreeProc **freeProcPtr); /* 180 */
    void (*tkDrawAngledTextLayout) (Display *display, Drawable drawable, GC gc, Tk_TextLayout layout, int x, int y, double angle, int firstChar, int lastChar); /* 181 */
    void (*tkUnderlineAngledTextLayout) (Display *display, Drawable drawable, GC gc, Tk_TextLayout layout, int x, int y, double angle, int underline); /* 182 */
    int (*tkIntersectAngledTextLayout) (Tk_TextLayout layout, int x, int y, int width, int height, double angle); /* 183 */
    void (*tkDrawAngledChars) (Display *display, Drawable drawable, GC gc, Tk_Font tkfont, const char *source, int numBytes, double x, double y, double angle); /* 184 */
#if !defined(_WIN32) && !defined(MAC_OSX_TCL) /* UNIX */
    void (*reserved185)(void);
#endif /* UNIX */
#if defined(_WIN32) /* WIN */
    void (*reserved185)(void);
#endif /* WIN */
#ifdef MAC_OSX_TCL /* MACOSX */
    void (*tkpRedrawWidget) (Tk_Window tkwin); /* 185 */
#endif /* MACOSX */
#if !defined(_WIN32) && !defined(MAC_OSX_TCL) /* UNIX */
    void (*reserved186)(void);
#endif /* UNIX */
#if defined(_WIN32) /* WIN */
    void (*reserved186)(void);
#endif /* WIN */
#ifdef MAC_OSX_TCL /* MACOSX */
    int (*tkpWillDrawWidget) (Tk_Window tkwin); /* 186 */
#endif /* MACOSX */
    void (*tkUnusedStubEntry) (void); /* 187 */
} TkIntStubs;

extern const TkIntStubs *tkIntStubsPtr;

#ifdef __cplusplus
}
#endif

#if defined(USE_TK_STUBS)

/*
 * Inline function declarations:
 */

#define TkAllocWindow \
	(tkIntStubsPtr->tkAllocWindow) /* 0 */
#define TkBezierPoints \
	(tkIntStubsPtr->tkBezierPoints) /* 1 */
#define TkBezierScreenPoints \
	(tkIntStubsPtr->tkBezierScreenPoints) /* 2 */
/* Slot 3 is reserved */
#define TkBindEventProc \
	(tkIntStubsPtr->tkBindEventProc) /* 4 */
#define TkBindFree \
	(tkIntStubsPtr->tkBindFree) /* 5 */
#define TkBindInit \
	(tkIntStubsPtr->tkBindInit) /* 6 */
#define TkChangeEventWindow \
	(tkIntStubsPtr->tkChangeEventWindow) /* 7 */
#define TkClipInit \
	(tkIntStubsPtr->tkClipInit) /* 8 */
#define TkComputeAnchor \
	(tkIntStubsPtr->tkComputeAnchor) /* 9 */
/* Slot 10 is reserved */
/* Slot 11 is reserved */
#define TkCreateCursorFromData \
	(tkIntStubsPtr->tkCreateCursorFromData) /* 12 */
#define TkCreateFrame \
	(tkIntStubsPtr->tkCreateFrame) /* 13 */
#define TkCreateMainWindow \
	(tkIntStubsPtr->tkCreateMainWindow) /* 14 */
#define TkCurrentTime \
	(tkIntStubsPtr->tkCurrentTime) /* 15 */
#define TkDeleteAllImages \
	(tkIntStubsPtr->tkDeleteAllImages) /* 16 */
#define TkDoConfigureNotify \
	(tkIntStubsPtr->tkDoConfigureNotify) /* 17 */
#define TkDrawInsetFocusHighlight \
	(tkIntStubsPtr->tkDrawInsetFocusHighlight) /* 18 */
#define TkEventDeadWindow \
	(tkIntStubsPtr->tkEventDeadWindow) /* 19 */
#define TkFillPolygon \
	(tkIntStubsPtr->tkFillPolygon) /* 20 */
#define TkFindStateNum \
	(tkIntStubsPtr->tkFindStateNum) /* 21 */
#define TkFindStateString \
	(tkIntStubsPtr->tkFindStateString) /* 22 */
#define TkFocusDeadWindow \
	(tkIntStubsPtr->tkFocusDeadWindow) /* 23 */
#define TkFocusFilterEvent \
	(tkIntStubsPtr->tkFocusFilterEvent) /* 24 */
#define TkFocusKeyEvent \
	(tkIntStubsPtr->tkFocusKeyEvent) /* 25 */
#define TkFontPkgInit \
	(tkIntStubsPtr->tkFontPkgInit) /* 26 */
#define TkFontPkgFree \
	(tkIntStubsPtr->tkFontPkgFree) /* 27 */
#define TkFreeBindingTags \
	(tkIntStubsPtr->tkFreeBindingTags) /* 28 */
#define TkpFreeCursor \
	(tkIntStubsPtr->tkpFreeCursor) /* 29 */
#define TkGetBitmapData \
	(tkIntStubsPtr->tkGetBitmapData) /* 30 */
#define TkGetButtPoints \
	(tkIntStubsPtr->tkGetButtPoints) /* 31 */
#define TkGetCursorByName \
	(tkIntStubsPtr->tkGetCursorByName) /* 32 */
#define TkGetDefaultScreenName \
	(tkIntStubsPtr->tkGetDefaultScreenName) /* 33 */
#define TkGetDisplay \
	(tkIntStubsPtr->tkGetDisplay) /* 34 */
#define TkGetDisplayOf \
	(tkIntStubsPtr->tkGetDisplayOf) /* 35 */
#define TkGetFocusWin \
	(tkIntStubsPtr->tkGetFocusWin) /* 36 */
#define TkGetInterpNames \
	(tkIntStubsPtr->tkGetInterpNames) /* 37 */
#define TkGetMiterPoints \
	(tkIntStubsPtr->tkGetMiterPoints) /* 38 */
#define TkGetPointerCoords \
	(tkIntStubsPtr->tkGetPointerCoords) /* 39 */
#define TkGetServerInfo \
	(tkIntStubsPtr->tkGetServerInfo) /* 40 */
#define TkGrabDeadWindow \
	(tkIntStubsPtr->tkGrabDeadWindow) /* 41 */
#define TkGrabState \
	(tkIntStubsPtr->tkGrabState) /* 42 */
#define TkIncludePoint \
	(tkIntStubsPtr->tkIncludePoint) /* 43 */
#define TkInOutEvents \
	(tkIntStubsPtr->tkInOutEvents) /* 44 */
#define TkInstallFrameMenu \
	(tkIntStubsPtr->tkInstallFrameMenu) /* 45 */
#define TkKeysymToString \
	(tkIntStubsPtr->tkKeysymToString) /* 46 */
#define TkLineToArea \
	(tkIntStubsPtr->tkLineToArea) /* 47 */
#define TkLineToPoint \
	(tkIntStubsPtr->tkLineToPoint) /* 48 */
#define TkMakeBezierCurve \
	(tkIntStubsPtr->tkMakeBezierCurve) /* 49 */
#define TkMakeBezierPostscript \
	(tkIntStubsPtr->tkMakeBezierPostscript) /* 50 */
#define TkOptionClassChanged \
	(tkIntStubsPtr->tkOptionClassChanged) /* 51 */
#define TkOptionDeadWindow \
	(tkIntStubsPtr->tkOptionDeadWindow) /* 52 */
#define TkOvalToArea \
	(tkIntStubsPtr->tkOvalToArea) /* 53 */
#define TkOvalToPoint \
	(tkIntStubsPtr->tkOvalToPoint) /* 54 */
#define TkpChangeFocus \
	(tkIntStubsPtr->tkpChangeFocus) /* 55 */
#define TkpCloseDisplay \
	(tkIntStubsPtr->tkpCloseDisplay) /* 56 */
#define TkpClaimFocus \
	(tkIntStubsPtr->tkpClaimFocus) /* 57 */
#define TkpDisplayWarning \
	(tkIntStubsPtr->tkpDisplayWarning) /* 58 */
#define TkpGetAppName \
	(tkIntStubsPtr->tkpGetAppName) /* 59 */
#define TkpGetOtherWindow \
	(tkIntStubsPtr->tkpGetOtherWindow) /* 60 */
#define TkpGetWrapperWindow \
	(tkIntStubsPtr->tkpGetWrapperWindow) /* 61 */
#define TkpInit \
	(tkIntStubsPtr->tkpInit) /* 62 */
#define TkpInitializeMenuBindings \
	(tkIntStubsPtr->tkpInitializeMenuBindings) /* 63 */
#define TkpMakeContainer \
	(tkIntStubsPtr->tkpMakeContainer) /* 64 */
#define TkpMakeMenuWindow \
	(tkIntStubsPtr->tkpMakeMenuWindow) /* 65 */
#define TkpMakeWindow \
	(tkIntStubsPtr->tkpMakeWindow) /* 66 */
#define TkpMenuNotifyToplevelCreate \
	(tkIntStubsPtr->tkpMenuNotifyToplevelCreate) /* 67 */
#define TkpOpenDisplay \
	(tkIntStubsPtr->tkpOpenDisplay) /* 68 */
#define TkPointerEvent \
	(tkIntStubsPtr->tkPointerEvent) /* 69 */
#define TkPolygonToArea \
	(tkIntStubsPtr->tkPolygonToArea) /* 70 */
#define TkPolygonToPoint \
	(tkIntStubsPtr->tkPolygonToPoint) /* 71 */
#define TkPositionInTree \
	(tkIntStubsPtr->tkPositionInTree) /* 72 */
#define TkpRedirectKeyEvent \
	(tkIntStubsPtr->tkpRedirectKeyEvent) /* 73 */
#define TkpSetMainMenubar \
	(tkIntStubsPtr->tkpSetMainMenubar) /* 74 */
#define TkpUseWindow \
	(tkIntStubsPtr->tkpUseWindow) /* 75 */
/* Slot 76 is reserved */
#define TkQueueEventForAllChildren \
	(tkIntStubsPtr->tkQueueEventForAllChildren) /* 77 */
#define TkReadBitmapFile \
	(tkIntStubsPtr->tkReadBitmapFile) /* 78 */
#define TkScrollWindow \
	(tkIntStubsPtr->tkScrollWindow) /* 79 */
#define TkSelDeadWindow \
	(tkIntStubsPtr->tkSelDeadWindow) /* 80 */
#define TkSelEventProc \
	(tkIntStubsPtr->tkSelEventProc) /* 81 */
#define TkSelInit \
	(tkIntStubsPtr->tkSelInit) /* 82 */
#define TkSelPropProc \
	(tkIntStubsPtr->tkSelPropProc) /* 83 */
/* Slot 84 is reserved */
#define TkSetWindowMenuBar \
	(tkIntStubsPtr->tkSetWindowMenuBar) /* 85 */
#define TkStringToKeysym \
	(tkIntStubsPtr->tkStringToKeysym) /* 86 */
#define TkThickPolyLineToArea \
	(tkIntStubsPtr->tkThickPolyLineToArea) /* 87 */
#define TkWmAddToColormapWindows \
	(tkIntStubsPtr->tkWmAddToColormapWindows) /* 88 */
#define TkWmDeadWindow \
	(tkIntStubsPtr->tkWmDeadWindow) /* 89 */
#define TkWmFocusToplevel \
	(tkIntStubsPtr->tkWmFocusToplevel) /* 90 */
#define TkWmMapWindow \
	(tkIntStubsPtr->tkWmMapWindow) /* 91 */
#define TkWmNewWindow \
	(tkIntStubsPtr->tkWmNewWindow) /* 92 */
#define TkWmProtocolEventProc \
	(tkIntStubsPtr->tkWmProtocolEventProc) /* 93 */
#define TkWmRemoveFromColormapWindows \
	(tkIntStubsPtr->tkWmRemoveFromColormapWindows) /* 94 */
#define TkWmRestackToplevel \
	(tkIntStubsPtr->tkWmRestackToplevel) /* 95 */
#define TkWmSetClass \
	(tkIntStubsPtr->tkWmSetClass) /* 96 */
#define TkWmUnmapWindow \
	(tkIntStubsPtr->tkWmUnmapWindow) /* 97 */
#define TkDebugBitmap \
	(tkIntStubsPtr->tkDebugBitmap) /* 98 */
#define TkDebugBorder \
	(tkIntStubsPtr->tkDebugBorder) /* 99 */
#define TkDebugCursor \
	(tkIntStubsPtr->tkDebugCursor) /* 100 */
#define TkDebugColor \
	(tkIntStubsPtr->tkDebugColor) /* 101 */
#define TkDebugConfig \
	(tkIntStubsPtr->tkDebugConfig) /* 102 */
#define TkDebugFont \
	(tkIntStubsPtr->tkDebugFont) /* 103 */
#define TkFindStateNumObj \
	(tkIntStubsPtr->tkFindStateNumObj) /* 104 */
#define TkGetBitmapPredefTable \
	(tkIntStubsPtr->tkGetBitmapPredefTable) /* 105 */
#define TkGetDisplayList \
	(tkIntStubsPtr->tkGetDisplayList) /* 106 */
#define TkGetMainInfoList \
	(tkIntStubsPtr->tkGetMainInfoList) /* 107 */
#define TkGetWindowFromObj \
	(tkIntStubsPtr->tkGetWindowFromObj) /* 108 */
#define TkpGetString \
	(tkIntStubsPtr->tkpGetString) /* 109 */
#define TkpGetSubFonts \
	(tkIntStubsPtr->tkpGetSubFonts) /* 110 */
#define TkpGetSystemDefault \
	(tkIntStubsPtr->tkpGetSystemDefault) /* 111 */
#define TkpMenuThreadInit \
	(tkIntStubsPtr->tkpMenuThreadInit) /* 112 */
#define TkClipBox \
	(tkIntStubsPtr->tkClipBox) /* 113 */
#define TkCreateRegion \
	(tkIntStubsPtr->tkCreateRegion) /* 114 */
#define TkDestroyRegion \
	(tkIntStubsPtr->tkDestroyRegion) /* 115 */
#define TkIntersectRegion \
	(tkIntStubsPtr->tkIntersectRegion) /* 116 */
#define TkRectInRegion \
	(tkIntStubsPtr->tkRectInRegion) /* 117 */
#define TkSetRegion \
	(tkIntStubsPtr->tkSetRegion) /* 118 */
#define TkUnionRectWithRegion \
	(tkIntStubsPtr->tkUnionRectWithRegion) /* 119 */
/* Slot 120 is reserved */
#ifdef MAC_OSX_TK /* AQUA */
#define TkpCreateNativeBitmap \
	(tkIntStubsPtr->tkpCreateNativeBitmap) /* 121 */
#endif /* AQUA */
#ifdef MAC_OSX_TK /* AQUA */
#define TkpDefineNativeBitmaps \
	(tkIntStubsPtr->tkpDefineNativeBitmaps) /* 122 */
#endif /* AQUA */
/* Slot 123 is reserved */
#ifdef MAC_OSX_TK /* AQUA */
#define TkpGetNativeAppBitmap \
	(tkIntStubsPtr->tkpGetNativeAppBitmap) /* 124 */
#endif /* AQUA */
/* Slot 125 is reserved */
/* Slot 126 is reserved */
/* Slot 127 is reserved */
/* Slot 128 is reserved */
/* Slot 129 is reserved */
/* Slot 130 is reserved */
/* Slot 131 is reserved */
/* Slot 132 is reserved */
/* Slot 133 is reserved */
/* Slot 134 is reserved */
#define TkpDrawHighlightBorder \
	(tkIntStubsPtr->tkpDrawHighlightBorder) /* 135 */
#define TkSetFocusWin \
	(tkIntStubsPtr->tkSetFocusWin) /* 136 */
#define TkpSetKeycodeAndState \
	(tkIntStubsPtr->tkpSetKeycodeAndState) /* 137 */
#define TkpGetKeySym \
	(tkIntStubsPtr->tkpGetKeySym) /* 138 */
#define TkpInitKeymapInfo \
	(tkIntStubsPtr->tkpInitKeymapInfo) /* 139 */
#define TkPhotoGetValidRegion \
	(tkIntStubsPtr->tkPhotoGetValidRegion) /* 140 */
#define TkWmStackorderToplevel \
	(tkIntStubsPtr->tkWmStackorderToplevel) /* 141 */
#define TkFocusFree \
	(tkIntStubsPtr->tkFocusFree) /* 142 */
#define TkClipCleanup \
	(tkIntStubsPtr->tkClipCleanup) /* 143 */
#define TkGCCleanup \
	(tkIntStubsPtr->tkGCCleanup) /* 144 */
#define TkSubtractRegion \
	(tkIntStubsPtr->tkSubtractRegion) /* 145 */
#define TkStylePkgInit \
	(tkIntStubsPtr->tkStylePkgInit) /* 146 */
#define TkStylePkgFree \
	(tkIntStubsPtr->tkStylePkgFree) /* 147 */
#define TkToplevelWindowForCommand \
	(tkIntStubsPtr->tkToplevelWindowForCommand) /* 148 */
#define TkGetOptionSpec \
	(tkIntStubsPtr->tkGetOptionSpec) /* 149 */
#define TkMakeRawCurve \
	(tkIntStubsPtr->tkMakeRawCurve) /* 150 */
#define TkMakeRawCurvePostscript \
	(tkIntStubsPtr->tkMakeRawCurvePostscript) /* 151 */
#define TkpDrawFrame \
	(tkIntStubsPtr->tkpDrawFrame) /* 152 */
#define TkCreateThreadExitHandler \
	(tkIntStubsPtr->tkCreateThreadExitHandler) /* 153 */
#define TkDeleteThreadExitHandler \
	(tkIntStubsPtr->tkDeleteThreadExitHandler) /* 154 */
/* Slot 155 is reserved */
#define TkpTestembedCmd \
	(tkIntStubsPtr->tkpTestembedCmd) /* 156 */
#define TkpTesttextCmd \
	(tkIntStubsPtr->tkpTesttextCmd) /* 157 */
#define TkSelGetSelection \
	(tkIntStubsPtr->tkSelGetSelection) /* 158 */
#define TkTextGetIndex \
	(tkIntStubsPtr->tkTextGetIndex) /* 159 */
#define TkTextIndexBackBytes \
	(tkIntStubsPtr->tkTextIndexBackBytes) /* 160 */
#define TkTextIndexForwBytes \
	(tkIntStubsPtr->tkTextIndexForwBytes) /* 161 */
#define TkTextMakeByteIndex \
	(tkIntStubsPtr->tkTextMakeByteIndex) /* 162 */
#define TkTextPrintIndex \
	(tkIntStubsPtr->tkTextPrintIndex) /* 163 */
#define TkTextSetMark \
	(tkIntStubsPtr->tkTextSetMark) /* 164 */
#define TkTextXviewCmd \
	(tkIntStubsPtr->tkTextXviewCmd) /* 165 */
#define TkTextChanged \
	(tkIntStubsPtr->tkTextChanged) /* 166 */
#define TkBTreeNumLines \
	(tkIntStubsPtr->tkBTreeNumLines) /* 167 */
#define TkTextInsertDisplayProc \
	(tkIntStubsPtr->tkTextInsertDisplayProc) /* 168 */
#define TkStateParseProc \
	(tkIntStubsPtr->tkStateParseProc) /* 169 */
#define TkStatePrintProc \
	(tkIntStubsPtr->tkStatePrintProc) /* 170 */
#define TkCanvasDashParseProc \
	(tkIntStubsPtr->tkCanvasDashParseProc) /* 171 */
#define TkCanvasDashPrintProc \
	(tkIntStubsPtr->tkCanvasDashPrintProc) /* 172 */
#define TkOffsetParseProc \
	(tkIntStubsPtr->tkOffsetParseProc) /* 173 */
#define TkOffsetPrintProc \
	(tkIntStubsPtr->tkOffsetPrintProc) /* 174 */
#define TkPixelParseProc \
	(tkIntStubsPtr->tkPixelParseProc) /* 175 */
#define TkPixelPrintProc \
	(tkIntStubsPtr->tkPixelPrintProc) /* 176 */
#define TkOrientParseProc \
	(tkIntStubsPtr->tkOrientParseProc) /* 177 */
#define TkOrientPrintProc \
	(tkIntStubsPtr->tkOrientPrintProc) /* 178 */
#define TkSmoothParseProc \
	(tkIntStubsPtr->tkSmoothParseProc) /* 179 */
#define TkSmoothPrintProc \
	(tkIntStubsPtr->tkSmoothPrintProc) /* 180 */
#define TkDrawAngledTextLayout \
	(tkIntStubsPtr->tkDrawAngledTextLayout) /* 181 */
#define TkUnderlineAngledTextLayout \
	(tkIntStubsPtr->tkUnderlineAngledTextLayout) /* 182 */
#define TkIntersectAngledTextLayout \
	(tkIntStubsPtr->tkIntersectAngledTextLayout) /* 183 */
#define TkDrawAngledChars \
	(tkIntStubsPtr->tkDrawAngledChars) /* 184 */
#ifdef MAC_OSX_TCL /* MACOSX */
#define TkpRedrawWidget \
	(tkIntStubsPtr->tkpRedrawWidget) /* 185 */
#endif /* MACOSX */
#ifdef MAC_OSX_TCL /* MACOSX */
#define TkpWillDrawWidget \
	(tkIntStubsPtr->tkpWillDrawWidget) /* 186 */
#endif /* MACOSX */
#define TkUnusedStubEntry \
	(tkIntStubsPtr->tkUnusedStubEntry) /* 187 */

#endif /* defined(USE_TK_STUBS) */

/* !END!: Do not edit above this line. */

#undef TCL_STORAGE_CLASS
#define TCL_STORAGE_CLASS DLLIMPORT

/*
 * On X11, these macros are just wrappers for the equivalent X Region calls.
 */
#if !(defined(_WIN32) || defined(__CYGWIN__) || defined(MAC_OSX_TK)) /* X11 */

#undef TkClipBox
#undef TkCreateRegion
#undef TkDestroyRegion
#undef TkIntersectRegion
#undef TkRectInRegion
#undef TkSetRegion
#undef TkSubtractRegion
#undef TkUnionRectWithRegion
#undef TkpCmapStressed_
#undef TkpSync_
#undef TkUnixContainerId_
#undef TkUnixDoOneXEvent_
#undef TkUnixSetMenubar_
#undef TkWmCleanup_
#undef TkSendCleanup_
#undef TkpTestsendCmd_

#define TkClipBox(rgn, rect) XClipBox((Region) rgn, rect)
#define TkCreateRegion() (TkRegion) XCreateRegion()
#define TkDestroyRegion(rgn) XDestroyRegion((Region) rgn)
#define TkIntersectRegion(a, b, r) XIntersectRegion((Region) a, \
	(Region) b, (Region) r)
#define TkRectInRegion(r, x, y, w, h) XRectInRegion((Region) r, x, y, w, h)
#define TkSetRegion(d, gc, rgn) XSetRegion(d, gc, (Region) rgn)
#define TkSubtractRegion(a, b, r) XSubtractRegion((Region) a, \
	(Region) b, (Region) r)
#define TkUnionRectWithRegion(rect, src, ret) XUnionRectWithRegion(rect, \
	(Region) src, (Region) ret)

#endif /* UNIX */

#if !defined(MAC_OSX_TK)
#   undef TkpWillDrawWidget
#   undef TkpRedrawWidget
#   define TkpWillDrawWidget(w) 0
#   define TkpRedrawWidget(w)
#endif

#undef TkUnusedStubEntry

#endif /* _TKINTDECLS */

