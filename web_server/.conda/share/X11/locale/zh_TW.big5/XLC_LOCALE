#
# (c) 1996, X11R6 L10N for Taiwan and Big5 Encoding Project
#
# modified for X11R6.3 by <PERSON><PERSON><PERSON> <<EMAIL>> 1998/01/10
#
#
#	XLC_FONTSET category
#
XLC_FONTSET
#       fs0 class (7 bit ASCII)
fs0     {
        charset {
                name    ISO8859-1:GL
        }
        font    {
                primary         ISO8859-1:GL
                vertical_rotate all
        }
}
#       fs1 class
fs1     {
        charset {
                name    BIG5-0:<PERSON><PERSON><PERSON>
        }
        font    {
                primary BIG5-0:GLGR
				substitute BIG5-0:<PERSON><PERSON>GR
        }
}
END XLC_FONTSET
#
#	XLC_XLOCALE category
#
XLC_XLOCALE
encoding_name		zh_TW.Big5
mb_cur_max		2
state_depend_encoding	False
wc_encoding_mask	\x00038000
wc_shift_bits		8
use_stdc_env		True
force_convert_to_mb	True
#	cs0 class
cs0	{
	side		GL:Default
	length		1
	wc_encoding	\x00000000
	ct_encoding	ISO8859-1:GL
}
#	cs1 class
cs1	{
	side		none
	length		2
	byte1		\xa1,\xf9
	byte2		\x40,\x7e;\xa1,\xfe
	wc_encoding	\x00008000
	ct_encoding	BIG5-0:GLGR:\x1b\x25\x2f\x32
	mb_conversion	[\xa140,\xf9fe]->\x2140
	ct_conversion	[\x2140,\x79fe]->\xa140
}
#	cs2 class
cs2	{
	side		none
	length		2
	byte1		\xa1,\xc7
	byte2		\x40,\x7e;\xa1,\xfe
	wc_encoding	\x00010000
	ct_encoding	BIG5-E0:GL;BIG5-E0:GR
	mb_conversion	[\xa140,\xa17e]->\x2121, [\xa1a1,\xa1bf]->\x2160, [\xa1c0,\xa1fe]->\x2221, [\xa240,\xa25e]->\x2260, [\xa25f,\xa27e]->\x2321, [\xa2a1,\xa2de]->\x2341, [\xa2df,\xa2fe]->\x2421, [\xa340,\xa37d]->\x2441, [\xa37e,\xa37e]->\x2521, [\xa3a1,\xa3fd]->\x2522, [\xa3fe,\xa3fe]->\x2621, [\xa440,\xa47e]->\x2622, [\xa4a1,\xa4be]->\x2661, [\xa4bf,\xa4fe]->\x2721, [\xa540,\xa55d]->\x2761, [\xa55e,\xa57e]->\x2821, [\xa5a1,\xa5dd]->\x2842, [\xa5de,\xa5fe]->\x2921, [\xa640,\xa67c]->\x2942, [\xa67d,\xa67e]->\x2a21, [\xa6a1,\xa6fc]->\x2a23, [\xa6fd,\xa6fe]->\x2b21, [\xa740,\xa77e]->\x2b23, [\xa7a1,\xa7bd]->\x2b62, [\xa7be,\xa7fe]->\x2c21, [\xa840,\xa85c]->\x2c62, [\xa85d,\xa87e]->\x2d21, [\xa8a1,\xa8dc]->\x2d43, [\xa8dd,\xa8fe]->\x2e21, [\xa940,\xa97b]->\x2e43, [\xa97c,\xa97e]->\x2f21, [\xa9a1,\xa9fb]->\x2f24, [\xa9fc,\xa9fe]->\x3021, [\xaa40,\xaa7e]->\x3024, [\xaaa1,\xaabc]->\x3063, [\xaabd,\xaafe]->\x3121, [\xab40,\xab5b]->\x3163, [\xab5c,\xab7e]->\x3221, [\xaba1,\xabdb]->\x3244, [\xabdc,\xabfe]->\x3321, [\xac40,\xac7a]->\x3344, [\xac7b,\xac7e]->\x3421, [\xaca1,\xacfa]->\x3425, [\xacfb,\xacfe]->\x3521, [\xad40,\xad7e]->\x3525, [\xada1,\xadbb]->\x3564, [\xadbc,\xadfe]->\x3621, [\xae40,\xae5a]->\x3664, [\xae5b,\xae7e]->\x3721, [\xaea1,\xaeda]->\x3745, [\xaedb,\xaefe]->\x3821, [\xaf40,\xaf79]->\x3845, [\xaf7a,\xaf7e]->\x3921, [\xafa1,\xaff9]->\x3926, [\xaffa,\xaffe]->\x3a21, [\xb040,\xb07e]->\x3a26, [\xb0a1,\xb0ba]->\x3a65, [\xb0bb,\xb0fe]->\x3b21, [\xb140,\xb159]->\x3b65, [\xb15a,\xb17e]->\x3c21, [\xb1a1,\xb1d9]->\x3c46, [\xb1da,\xb1fe]->\x3d21, [\xb240,\xb278]->\x3d46, [\xb279,\xb27e]->\x3e21, [\xb2a1,\xb2f8]->\x3e27, [\xb2f9,\xb2fe]->\x3f21, [\xb340,\xb37e]->\x3f27, [\xb3a1,\xb3b9]->\x3f66, [\xb3ba,\xb3fe]->\x4021, [\xb440,\xb458]->\x4066, [\xb459,\xb47e]->\x4121, [\xb4a1,\xb4d8]->\x4147, [\xb4d9,\xb4fe]->\x4221, [\xb540,\xb577]->\x4247, [\xb578,\xb57e]->\x4321, [\xb5a1,\xb5f7]->\x4328, [\xb5f8,\xb5fe]->\x4421, [\xb640,\xb67e]->\x4428, [\xb6a1,\xb6b8]->\x4467, [\xb6b9,\xb6fe]->\x4521, [\xb740,\xb757]->\x4567, [\xb758,\xb77e]->\x4621, [\xb7a1,\xb7d7]->\x4648, [\xb7d8,\xb7fe]->\x4721, [\xb840,\xb876]->\x4748, [\xb877,\xb87e]->\x4821, [\xb8a1,\xb8f6]->\x4829, [\xb8f7,\xb8fe]->\x4921, [\xb940,\xb97e]->\x4929, [\xb9a1,\xb9b7]->\x4968, [\xb9b8,\xb9fe]->\x4a21, [\xba40,\xba56]->\x4a68, [\xba57,\xba7e]->\x4b21, [\xbaa1,\xbad6]->\x4b49, [\xbad7,\xbafe]->\x4c21, [\xbb40,\xbb75]->\x4c49, [\xbb76,\xbb7e]->\x4d21, [\xbba1,\xbbf5]->\x4d2a, [\xbbf6,\xbbfe]->\x4e21, [\xbc40,\xbc7e]->\x4e2a, [\xbca1,\xbcb6]->\x4e69, [\xbcb7,\xbcfe]->\x4f21, [\xbd40,\xbd55]->\x4f69, [\xbd56,\xbd7e]->\x5021, [\xbda1,\xbdd5]->\x504a, [\xbdd6,\xbdfe]->\x5121, [\xbe40,\xbe74]->\x514a, [\xbe75,\xbe7e]->\x5221, [\xbea1,\xbef4]->\x522b, [\xbef5,\xbefe]->\x5321, [\xbf40,\xbf7e]->\x532b, [\xbfa1,\xbfb5]->\x536a, [\xbfb6,\xbffe]->\x5421, [\xc040,\xc054]->\x546a, [\xc055,\xc07e]->\x5521, [\xc0a1,\xc0d4]->\x554b, [\xc0d5,\xc0fe]->\x5621, [\xc140,\xc173]->\x564b, [\xc174,\xc17e]->\x5721, [\xc1a1,\xc1f3]->\x572c, [\xc1f4,\xc1fe]->\x5821, [\xc240,\xc27e]->\x582c, [\xc2a1,\xc2b4]->\x586b, [\xc2b5,\xc2fe]->\x5921, [\xc340,\xc353]->\x596b, [\xc354,\xc37e]->\x5a21, [\xc3a1,\xc3d3]->\x5a4c, [\xc3d4,\xc3fe]->\x5b21, [\xc440,\xc472]->\x5b4c, [\xc473,\xc47e]->\x5c21, [\xc4a1,\xc4f2]->\x5c2d, [\xc4f3,\xc4fe]->\x5d21, [\xc540,\xc57e]->\x5d2d, [\xc5a1,\xc5b3]->\x5d6c, [\xc5b4,\xc5fe]->\x5e21, [\xc640,\xc652]->\x5e6c, [\xc653,\xc67e]->\x5f21, [\xc6a1,\xc6d2]->\x5f4d, [\xc6d3,\xc6fe]->\x6021, [\xc740,\xc771]->\x604d, [\xc772,\xc77e]->\x6121, [\xc7a1,\xc7f1]->\x612e, [\xc7f2,\xc7fe]->\x6221
}
#	cs3 class
cs3	{
	side		none
	length		2
	byte1		\xc9,\xf9
	byte2		\x40,\x7e;\xa1,\xfe
	wc_encoding	\x00020000
	ct_encoding	BIG5-E1:GL;BIG5-E1:GR
	mb_conversion	[\xc940,\xc97e]->\x2121, [\xc9a1,\xc9bf]->\x2160, [\xc9c0,\xc9fe]->\x2221, [\xca40,\xca5e]->\x2260, [\xca5f,\xca7e]->\x2321, [\xcaa1,\xcade]->\x2341, [\xcadf,\xcafe]->\x2421, [\xcb40,\xcb7d]->\x2441, [\xcb7e,\xcb7e]->\x2521, [\xcba1,\xcbfd]->\x2522, [\xcbfe,\xcbfe]->\x2621, [\xcc40,\xcc7e]->\x2622, [\xcca1,\xccbe]->\x2661, [\xccbf,\xccfe]->\x2721, [\xcd40,\xcd5d]->\x2761, [\xcd5e,\xcd7e]->\x2821, [\xcda1,\xcddd]->\x2842, [\xcdde,\xcdfe]->\x2921, [\xce40,\xce7c]->\x2942, [\xce7d,\xce7e]->\x2a21, [\xcea1,\xcefc]->\x2a23, [\xcefd,\xcefe]->\x2b21, [\xcf40,\xcf7e]->\x2b23, [\xcfa1,\xcfbd]->\x2b62, [\xcfbe,\xcffe]->\x2c21, [\xd040,\xd05c]->\x2c62, [\xd05d,\xd07e]->\x2d21, [\xd0a1,\xd0dc]->\x2d43, [\xd0dd,\xd0fe]->\x2e21, [\xd140,\xd17b]->\x2e43, [\xd17c,\xd17e]->\x2f21, [\xd1a1,\xd1fb]->\x2f24, [\xd1fc,\xd1fe]->\x3021, [\xd240,\xd27e]->\x3024, [\xd2a1,\xd2bc]->\x3063, [\xd2bd,\xd2fe]->\x3121, [\xd340,\xd35b]->\x3163, [\xd35c,\xd37e]->\x3221, [\xd3a1,\xd3db]->\x3244, [\xd3dc,\xd3fe]->\x3321, [\xd440,\xd47a]->\x3344, [\xd47b,\xd47e]->\x3421, [\xd4a1,\xd4fa]->\x3425, [\xd4fb,\xd4fe]->\x3521, [\xd540,\xd57e]->\x3525, [\xd5a1,\xd5bb]->\x3564, [\xd5bc,\xd5fe]->\x3621, [\xd640,\xd65a]->\x3664, [\xd65b,\xd67e]->\x3721, [\xd6a1,\xd6da]->\x3745, [\xd6db,\xd6fe]->\x3821, [\xd740,\xd779]->\x3845, [\xd77a,\xd77e]->\x3921, [\xd7a1,\xd7f9]->\x3926, [\xd7fa,\xd7fe]->\x3a21, [\xd840,\xd87e]->\x3a26, [\xd8a1,\xd8ba]->\x3a65, [\xd8bb,\xd8fe]->\x3b21, [\xd940,\xd959]->\x3b65, [\xd95a,\xd97e]->\x3c21, [\xd9a1,\xd9d9]->\x3c46, [\xd9da,\xd9fe]->\x3d21, [\xda40,\xda78]->\x3d46, [\xda79,\xda7e]->\x3e21, [\xdaa1,\xdaf8]->\x3e27, [\xdaf9,\xdafe]->\x3f21, [\xdb40,\xdb7e]->\x3f27, [\xdba1,\xdbb9]->\x3f66, [\xdbba,\xdbfe]->\x4021, [\xdc40,\xdc58]->\x4066, [\xdc59,\xdc7e]->\x4121, [\xdca1,\xdcd8]->\x4147, [\xdcd9,\xdcfe]->\x4221, [\xdd40,\xdd77]->\x4247, [\xdd78,\xdd7e]->\x4321, [\xdda1,\xddf7]->\x4328, [\xddf8,\xddfe]->\x4421, [\xde40,\xde7e]->\x4428, [\xdea1,\xdeb8]->\x4467, [\xdeb9,\xdefe]->\x4521, [\xdf40,\xdf57]->\x4567, [\xdf58,\xdf7e]->\x4621, [\xdfa1,\xdfd7]->\x4648, [\xdfd8,\xdffe]->\x4721, [\xe040,\xe076]->\x4748, [\xe077,\xe07e]->\x4821, [\xe0a1,\xe0f6]->\x4829, [\xe0f7,\xe0fe]->\x4921, [\xe140,\xe17e]->\x4929, [\xe1a1,\xe1b7]->\x4968, [\xe1b8,\xe1fe]->\x4a21, [\xe240,\xe256]->\x4a68, [\xe257,\xe27e]->\x4b21, [\xe2a1,\xe2d6]->\x4b49, [\xe2d7,\xe2fe]->\x4c21, [\xe340,\xe375]->\x4c49, [\xe376,\xe37e]->\x4d21, [\xe3a1,\xe3f5]->\x4d2a, [\xe3f6,\xe3fe]->\x4e21, [\xe440,\xe47e]->\x4e2a, [\xe4a1,\xe4b6]->\x4e69, [\xe4b7,\xe4fe]->\x4f21, [\xe540,\xe555]->\x4f69, [\xe556,\xe57e]->\x5021, [\xe5a1,\xe5d5]->\x504a, [\xe5d6,\xe5fe]->\x5121, [\xe640,\xe674]->\x514a, [\xe675,\xe67e]->\x5221, [\xe6a1,\xe6f4]->\x522b, [\xe6f5,\xe6fe]->\x5321, [\xe740,\xe77e]->\x532b, [\xe7a1,\xe7b5]->\x536a, [\xe7b6,\xe7fe]->\x5421, [\xe840,\xe854]->\x546a, [\xe855,\xe87e]->\x5521, [\xe8a1,\xe8d4]->\x554b, [\xe8d5,\xe8fe]->\x5621, [\xe940,\xe973]->\x564b, [\xe974,\xe97e]->\x5721, [\xe9a1,\xe9f3]->\x572c, [\xe9f4,\xe9fe]->\x5821, [\xea40,\xea7e]->\x582c, [\xeaa1,\xeab4]->\x586b, [\xeab5,\xeafe]->\x5921, [\xeb40,\xeb53]->\x596b, [\xeb54,\xeb7e]->\x5a21, [\xeba1,\xebd3]->\x5a4c, [\xebd4,\xebfe]->\x5b21, [\xec40,\xec72]->\x5b4c, [\xec73,\xec7e]->\x5c21, [\xeca1,\xecf2]->\x5c2d, [\xecf3,\xecfe]->\x5d21, [\xed40,\xed7e]->\x5d2d, [\xeda1,\xedb3]->\x5d6c, [\xedb4,\xedfe]->\x5e21, [\xee40,\xee52]->\x5e6c, [\xee53,\xee7e]->\x5f21, [\xeea1,\xeed2]->\x5f4d, [\xeed3,\xeefe]->\x6021, [\xef40,\xef71]->\x604d, [\xef72,\xef7e]->\x6121, [\xefa1,\xeff1]->\x612e, [\xeff2,\xeffe]->\x6221, [\xf040,\xf07e]->\x622e, [\xf0a1,\xf0b2]->\x626d, [\xf0b3,\xf0fe]->\x6321, [\xf140,\xf151]->\x636d, [\xf152,\xf17e]->\x6421, [\xf1a1,\xf1d1]->\x644e, [\xf1d2,\xf1fe]->\x6521, [\xf240,\xf270]->\x654e, [\xf271,\xf27e]->\x6621, [\xf2a1,\xf2f0]->\x662f, [\xf2f1,\xf2fe]->\x6721, [\xf340,\xf37e]->\x672f, [\xf3a1,\xf3b1]->\x676e, [\xf3b2,\xf3fe]->\x6821, [\xf440,\xf450]->\x686e, [\xf451,\xf47e]->\x6921, [\xf4a1,\xf4d0]->\x694f, [\xf4d1,\xf4fe]->\x6a21, [\xf540,\xf56f]->\x6a4f, [\xf570,\xf57e]->\x6b21, [\xf5a1,\xf5ef]->\x6b30, [\xf5f0,\xf5fe]->\x6c21, [\xf640,\xf67e]->\x6c30, [\xf6a1,\xf6b0]->\x6c6f, [\xf6b1,\xf6fe]->\x6d21, [\xf740,\xf74f]->\x6d6f, [\xf750,\xf77e]->\x6e21, [\xf7a1,\xf7cf]->\x6e50, [\xf7d0,\xf7fe]->\x6f21, [\xf840,\xf86e]->\x6f50, [\xf86f,\xf87e]->\x7021, [\xf8a1,\xf8ee]->\x7031, [\xf8ef,\xf8fe]->\x7121, [\xf940,\xf97e]->\x7131, [\xf9a1,\xf9af]->\x7170, [\xf9b0,\xf9fe]->\x7221
}
END XLC_XLOCALE
