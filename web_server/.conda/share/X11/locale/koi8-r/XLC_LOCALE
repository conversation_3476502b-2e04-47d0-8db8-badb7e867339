#  XLocale Database Sample for koi8-r.
# 
# 
# 
# 	XLC_FONTSET category
# 
XLC_FONTSET
# 	fs0 class (7 bit ASCII)
fs0	{
	charset	{
		name		ISO8859-1:GL
	}
	font	{
		primary		KOI8-R:G<PERSON>
		substitute	ISO8859-1:GL
	}
}
# 	fs1 class 
fs1	{
	charset         KOI8-R:GR
	font            KOI8-R:GR
}
END XLC_FONTSET
#
#	XLC_CHARSET_DEFINE category
#
XLC_CHARSET_DEFINE
csd0	{
	charset_name	KOI8-R
	side		GR
	length		1
	string_encoding	False
	sequence	\x1b%/1
}
END XLC_CHARSET_DEFINE
# 
# 	XLC_XLOCALE category
# 
XLC_XLOCALE
encoding_name           KOI8-R
mb_cur_max		1
state_depend_encoding	False
wc_encoding_mask	\x30000000
wc_shift_bits		7
use_stdc_env		True
# 	cs0 class
cs0	{
	side		GL:Default
	length		1
	wc_encoding	\x00000000
	ct_encoding     KOI8-R:GL; ISO8859-1:GL
}
# 	cs1 class
cs1	{
	side		GR:Default
	length		1
	wc_encoding	\x30000000
	ct_encoding     KOI8-R:GR
}
END XLC_XLOCALE
