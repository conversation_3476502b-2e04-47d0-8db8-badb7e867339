This directory contains a collection of programs to demonstrate
the features of the Tk toolkit.  The programs are all scripts for
"wish", a windowing shell.  If wish has been installed on your path
then you can invoke any of the programs in this directory just
by typing its file name to your command shell under Unix.  Otherwise
invoke wish with the file as its first argument, e.g., "wish hello".
The rest of this file contains a brief description of each program.
Files with names ending in ".tcl" are procedure packages used by one
or more of the demo programs;  they can't be used as programs by
themselves so they aren't described below.

hello -		Creates a single button;  if you click on it, a message
		is typed and the application terminates.

widget -	Contains a collection of demonstrations of the widgets
		currently available in the Tk library.  Most of the .tcl
		files are scripts for individual demos available through
		the "widget" program.

ixset -		A simple Tk-based wrapper for the "xset" program, which
		allows you to interactively query and set various X options
		such as mouse acceleration and bell volume.  Thanks to
		<PERSON> for contributing this example.

rolodex -	A mock-up of a simple rolodex application.  It has much of
		the user interface for such an application but no back-end
		database.  This program was written in response to <PERSON>'s toolkit benchmark challenge.

tcolor -	A color editor.  Allows you to edit colors in several
		different ways, and will also perform automatic updates
		using "send".

rmt -		Allows you to "hook-up" remotely to any Tk application
		on the display.  Select an application with the menu,
		then just type commands:  they'll go to that application.

timer -		Displays a seconds timer with start and stop buttons.
		Control-c and control-q cause it to exit.

browse -	A simple directory browser.  Invoke it with and argument
		giving the name of the directory you'd like to browse.
		Double-click on files or subdirectories to browse them.
		Control-c and control-q cause the program to exit.
